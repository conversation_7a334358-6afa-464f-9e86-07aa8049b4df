import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import SessionStorageManager from '@/utils/sessionStorage';

interface PublicRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

/**
 * PublicRoute handles routing for unauthenticated users in the new subscription-first flow.
 * Logic:
 * 1. If user is authenticated and has subscription, redirect to dashboard
 * 2. If user is authenticated but no subscription, redirect to subscription page
 * 3. If user has purchased plan but not registered, allow access to registration
 * 4. If user is not authenticated, allow access to public routes
 */
const PublicRoute: React.FC<PublicRouteProps> = ({
  children,
  redirectTo = '/dashboard'
}) => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const location = useLocation();

  // Show loading state while checking authentication
  if (isLoading) {
    return <div>Loading...</div>;
  }

  // Check if user has purchased a plan in session
  const hasPurchasedPlan = SessionStorageManager.hasPurchasedPlan();
  
  // If user is authenticated
  if (isAuthenticated) {
    // If user has a pricing plan, redirect to dashboard
    if (user?.pricingPlan) {
      return <Navigate to={redirectTo} replace />;
    } else {
      // User is authenticated but has no subscription, redirect to subscription page
      return <Navigate to="/auth/subscribe" replace />;
    }
  }

  // For registration page, check if user has purchased a plan
  if (location.pathname === '/auth/register') {
    if (!hasPurchasedPlan) {
      // No purchased plan, redirect to subscription page first
      return <Navigate to="/auth/subscribe" replace />;
    }
  }

  // User is not authenticated, allow access to public routes
  return <>{children}</>;
};

export default PublicRoute;
