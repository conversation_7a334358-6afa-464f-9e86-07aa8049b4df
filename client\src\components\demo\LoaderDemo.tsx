import React from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>ard<PERSON>oader, 
  <PERSON>ir<PERSON>eyInlineLoader 
} from '@/components/ui/heirkey-loader';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export default function LoaderDemo() {
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto space-y-8">
        <h1 className="text-3xl font-bold text-center text-[#1F4168] mb-8">
          HeirKey Loading Animations Demo
        </h1>

        {/* Different Variants */}
        <Card>
          <CardHeader>
            <CardTitle>Animation Variants</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div className="text-center">
                <h3 className="font-semibold mb-4">G<PERSON> (Default)</h3>
                <HeirKeyLoader variant="glow" size="md" text="Loading..." />
              </div>
              <div className="text-center">
                <h3 className="font-semibold mb-4">Pulse</h3>
                <HeirKeyLoader variant="pulse" size="md" text="Loading..." />
              </div>
              <div className="text-center">
                <h3 className="font-semibold mb-4">Spin</h3>
                <HeirKeyLoader variant="spin" size="md" text="Loading..." />
              </div>
              <div className="text-center">
                <h3 className="font-semibold mb-4">Float</h3>
                <HeirKeyLoader variant="float" size="md" text="Loading..." />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Different Sizes */}
        <Card>
          <CardHeader>
            <CardTitle>Size Variations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div className="text-center">
                <h3 className="font-semibold mb-4">Small</h3>
                <HeirKeyLoader size="sm" text="Loading..." />
              </div>
              <div className="text-center">
                <h3 className="font-semibold mb-4">Medium</h3>
                <HeirKeyLoader size="md" text="Loading..." />
              </div>
              <div className="text-center">
                <h3 className="font-semibold mb-4">Large</h3>
                <HeirKeyLoader size="lg" text="Loading..." />
              </div>
              <div className="text-center">
                <h3 className="font-semibold mb-4">Extra Large</h3>
                <HeirKeyLoader size="xl" text="Loading..." />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Inline Usage */}
        <Card>
          <CardHeader>
            <CardTitle>Inline Usage (Buttons)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button disabled className="w-full">
                <HeirKeyInlineLoader size="sm" className="mr-2" />
                Signing up...
              </Button>
              <Button disabled className="w-full">
                <HeirKeyInlineLoader size="md" className="mr-2" />
                Verifying...
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Card Usage */}
        <Card>
          <CardHeader>
            <CardTitle>Card Loading</CardTitle>
          </CardHeader>
          <CardContent>
            <Card className="border-2 border-dashed border-gray-300">
              <CardContent>
                <HeirKeyCardLoader text="Loading content..." />
              </CardContent>
            </Card>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
