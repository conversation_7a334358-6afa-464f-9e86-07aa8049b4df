import React from 'react';
import { cn } from '@/lib/utils';
import logo from '@/assets/global/logo.png';

interface HeirKeyLoaderProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showText?: boolean;
  text?: string;
  variant?: 'default' | 'pulse' | 'bounce' | 'spin' | 'glow' | 'float';
}

const sizeClasses = {
  sm: 'h-8 w-8',
  md: 'h-12 w-12',
  lg: 'h-16 w-16',
  xl: 'h-24 w-24'
};

const textSizeClasses = {
  sm: 'text-sm',
  md: 'text-base',
  lg: 'text-lg',
  xl: 'text-xl'
};

export function HeirKeyLoader({
  size = 'md',
  className,
  showText = true,
  text = 'Loading...',
  variant = 'glow'
}: HeirKeyLoaderProps) {
  const getAnimationClass = () => {
    switch (variant) {
      case 'spin':
        return 'animate-spin';
      case 'bounce':
        return 'animate-bounce';
      case 'pulse':
        return 'heirkey-pulse';
      case 'glow':
        return 'heirkey-pulse heirkey-glow';
      case 'float':
        return 'heirkey-float';
      default:
        return 'heirkey-pulse';
    }
  };

  return (
    <div className={cn('flex flex-col items-center justify-center space-y-4', className)}>
      <div className={cn('relative', sizeClasses[size])}>
        <img
          src={logo}
          alt="HeirKey Logo"
          className={cn(
            'w-full h-full object-contain drop-shadow-lg',
            getAnimationClass()
          )}
        />
        {(variant === 'pulse' || variant === 'glow') && (
          <div className={cn(
            'absolute inset-0 rounded-full bg-[#2BCFD5] opacity-10 animate-ping',
            sizeClasses[size]
          )} />
        )}
      </div>
      {showText && (
        <p className={cn(
          'font-medium text-[#1F4168] animate-pulse',
          textSizeClasses[size]
        )}>
          {text}
        </p>
      )}
    </div>
  );
}

// Full screen loading component
export function HeirKeyFullScreenLoader({
  text = 'Loading...',
  className
}: {
  text?: string;
  className?: string;
}) {
  return (
    <div className={cn(
      'min-h-screen bg-gradient-to-b from-white to-gray-50 flex items-center justify-center',
      className
    )}>
      <HeirKeyLoader size="xl" text={text} variant="glow" />
    </div>
  );
}

// Card loading component
export function HeirKeyCardLoader({
  text = 'Loading...',
  className
}: {
  text?: string;
  className?: string;
}) {
  return (
    <div className={cn('flex flex-col items-center justify-center py-12', className)}>
      <HeirKeyLoader size="lg" text={text} variant="glow" />
    </div>
  );
}

// Inline loading component (for buttons, etc.)
export function HeirKeyInlineLoader({ 
  size = 'sm', 
  className 
}: { 
  size?: 'sm' | 'md'; 
  className?: string; 
}) {
  return (
    <HeirKeyLoader 
      size={size} 
      showText={false} 
      variant="spin" 
      className={cn('inline-flex', className)} 
    />
  );
}
