"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const ownerSchema = new mongoose_1.default.Schema({
    userId: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        unique: true // Each user can only have one owner record
    },
    username: {
        type: String,
        trim: true
    },
    email: {
        type: String,
        required: true,
        trim: true,
        lowercase: true
    },
    firstName: {
        type: String,
        trim: true
    },
    lastName: {
        type: String,
        trim: true
    },
    image: {
        type: String
    },
    googleId: {
        type: String
    },
    externalUser: {
        type: Boolean,
        default: false
    },
    subscribedPlanId: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: 'SubscribedPlan',
        default: null
    },
    stripeSessionId: {
        type: String,
        default: null // Store the Stripe session ID for linking during registration
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Add indexes for better query performance
ownerSchema.index({ userId: 1 });
ownerSchema.index({ email: 1 });
ownerSchema.index({ username: 1 });
ownerSchema.index({ subscribedPlanId: 1 });
// Virtual to populate user data
ownerSchema.virtual('user', {
    ref: 'User',
    localField: 'userId',
    foreignField: '_id',
    justOne: true
});
// Virtual to populate subscription data
ownerSchema.virtual('subscription', {
    ref: 'SubscribedPlan',
    localField: 'subscribedPlanId',
    foreignField: '_id',
    justOne: true
});
// Instance method to get full name
ownerSchema.methods.getFullName = function () {
    if (this.firstName && this.lastName) {
        return `${this.firstName} ${this.lastName}`;
    }
    return this.firstName || this.lastName || this.username || 'Unknown';
};
// Static method to find owner by user ID
ownerSchema.statics.findByUserId = function (userId) {
    return this.findOne({ userId }).populate('user').populate('subscription');
};
// Static method to find owner by email
ownerSchema.statics.findByEmail = function (email) {
    return this.findOne({ email: email.toLowerCase() }).populate('user').populate('subscription');
};
const Owner = mongoose_1.default.model('Owner', ownerSchema);
exports.default = Owner;
//# sourceMappingURL=Owner.js.map