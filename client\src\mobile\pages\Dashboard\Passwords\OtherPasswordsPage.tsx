import { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Formik, Form, ErrorMessage, Field } from "formik";
import * as Yup from 'yup';
import GradiantHeader from "@/mobile/components/header/gradiantHeader";
import Footer from "@/mobile/components/layout/Footer";
import { CircularProgress } from '@/components/ui/CircularProgress';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { generateObjectId, convertUserInputToFormValues } from '@/services/userInputService';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  UserInput,
  fetchUserInputs,
  saveUserInput,
  selectLoading,
  selectQuestionsBySubcategoryId,
  selectUserInputsBySubcategoryId,
  updateUserInput,
  selectError,
} from '@/store/slices/passwordsSlice';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Plus, Trash2, Eye, EyeOff } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import passwordsData from '@/data/Passwords.json';
import ScrollToQuestion from '@/mobile/components/dashboard/Passwords/ScrollToQuestion';
import { QuestionItem, Question, buildValidationSchema, generateInitialValues, handleDependentAnswers } from '@/mobile/components/dashboard/Passwords/FormFields';

const OTHER_PASSWORDS_SECTION_ID = '1204';
const DROPDOWN_QUESTION_ID = 'p1';
const YESNO_QUESTION_ID = 'p2';

interface PasswordEntry {
  serviceName: string;
  customServiceName?: string;
  displayName?: string;
  accounts: AccountEntry[];
}

interface AccountEntry {
  username: string;
  password: string;
}

// Get questions from passwords data
const questions = passwordsData['1204'] || [];

function splitPasswordSteps(questions: any[]) {
  // Step 0: Yes/No questions (like "Do you want to save any other passwords?")
  const step0 = questions.filter(q => q.type === 'yesno');
  // Step 1: Service selection and password details (only if they answered yes)
  const step1 = questions.filter(q => q.type === 'choice');
  return [step0, step1];
}

// Pre-calculate steps to prevent recreation on every render
const steps = splitPasswordSteps(questions);

// Update the type definitions at the top
type FormValues = Record<string, string | string[]>;

// Build validation schema for the questions
const validationSchema = Yup.object().shape({
  [YESNO_QUESTION_ID]: Yup.string().required('Please select an option'),
});

export default function OtherPasswordsPage() {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const [savedAnswers, setSavedAnswers] = useState<Record<string, string | string[]>>({});
  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const [formError, setError] = useState<string | null>(null);
  const [step, setStep] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedServices, setSelectedServices] = useState<PasswordEntry[]>([]);
  const [duplicateServiceError, setDuplicateServiceError] = useState<string>('');
  const [selectedService, setSelectedService] = useState<string>('');
  const [customServiceName, setCustomServiceName] = useState<string>('');
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({});
  const servicesLoadedRef = useRef(false);

  // Get data from Redux store
  const userInputs = useAppSelector((state) => selectUserInputsBySubcategoryId('1204A')(state));
  const isLoadingRedux = useAppSelector(selectLoading);
  const reduxError = useAppSelector(selectError);
  const storeQuestions = useAppSelector(selectQuestionsBySubcategoryId(OTHER_PASSWORDS_SECTION_ID));

  // Get the questionId from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const targetQuestionId = queryParams.get('questionId');
  const fromReview = queryParams.get('fromReview');
  const serviceIndex = queryParams.get('serviceIndex');

  // Get dropdown options from the question
  const dropdownQuestion = storeQuestions.find(q => q.id === DROPDOWN_QUESTION_ID);
  const dropdownOptions = dropdownQuestion?.options || [];

  // Auto-navigate to correct step based on question ID when coming from review
  useEffect(() => {
    if (fromReview && targetQuestionId) {
      // Find which step contains the target question
      const questionStep = steps.findIndex(stepQuestions =>
        stepQuestions.some(q => q.id === targetQuestionId)
      );

      if (questionStep !== -1) {
        setStep(questionStep);
      } else {
        // Default to step 0 if question not found
        setStep(0);
      }
    } else if (fromReview) {
      // If coming from review but no specific question, go to step 0
      setStep(0);
    }
  }, [fromReview, targetQuestionId]);

  // Handle service highlighting when editing from review
  useEffect(() => {
    if (serviceIndex && selectedServices.length > 0) {
      const index = parseInt(serviceIndex);
      if (index >= 0 && index < selectedServices.length) {
        // Scroll to the specific service after a short delay
        setTimeout(() => {
          const serviceElement = document.getElementById(`service-${index}`);
          if (serviceElement) {
            serviceElement.scrollIntoView({
              behavior: 'smooth',
              block: 'center'
            });
            
            // Add highlighting
            serviceElement.classList.add('bg-yellow-50');
            serviceElement.classList.add('border-yellow-300');
            serviceElement.classList.add('border-2');
            serviceElement.classList.add('shadow-lg');
            serviceElement.classList.add('transition-all');
            serviceElement.classList.add('duration-1000');

            // Remove highlighting after 3 seconds
            setTimeout(() => {
              serviceElement.classList.remove('bg-yellow-50');
              serviceElement.classList.remove('border-yellow-300');
              serviceElement.classList.remove('border-2');
              serviceElement.classList.remove('shadow-lg');
            }, 3000);
          }
        }, 1000); // Wait for services to load
      }
    }
  }, [serviceIndex, selectedServices]);

  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchUserAnswers = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        setIsLoading(false);
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserAnswers();
  }, [dispatch, user]);

  // Process user inputs when they are loaded
  useEffect(() => {
    // Reset the ref when userInputs change
    servicesLoadedRef.current = false;

    if (userInputs && userInputs.length > 0) {
      // Use the first matching record
      const userInput = userInputs[0];

      // Only update state if we have a new ID or if it's the first time
      if (userInput._id && userInput._id !== existingInputId) {
        setExistingInputId(userInput._id);

        // Convert the saved answers to form values
        const formValues = convertUserInputToFormValues(userInput);
        setSavedAnswers(formValues);
      } else if (!existingInputId && userInput._id) {
        // First time setting the ID
        setExistingInputId(userInput._id);

        // Convert the saved answers to form values
        const formValues = convertUserInputToFormValues(userInput);
        setSavedAnswers(formValues);
      }

      // Load saved services from user input - only if we haven't loaded them yet
      if (userInput && userInput.answersBySection && !servicesLoadedRef.current) {
        const services: PasswordEntry[] = [];

        userInput.answersBySection.forEach(section => {
          section.answers.forEach(answer => {
            if (answer.originalQuestionId === DROPDOWN_QUESTION_ID) {
              try {
                const parsedData = JSON.parse(answer.answer);

                // Handle both old format (direct array) and new format (wrapped object)
                let serviceData = parsedData;
                if (parsedData && typeof parsedData === 'object' && parsedData.services) {
                  serviceData = parsedData.services; // New format with wrapper
                }

                if (Array.isArray(serviceData)) {
                  serviceData.forEach((item: any) => {
                    if (typeof item === 'object' && item.serviceName) {
                      const serviceEntry: PasswordEntry = {
                        serviceName: item.serviceName,
                        accounts: item.accounts || [{ username: item.username || '', password: item.password || '' }]
                      };

                      if (item.serviceName === 'Other' && item.customServiceName) {
                        serviceEntry.customServiceName = item.customServiceName;
                        serviceEntry.displayName = item.customServiceName;
                      } else if (item.displayName) {
                        serviceEntry.displayName = item.displayName;
                      } else {
                        serviceEntry.displayName = item.serviceName;
                      }

                      services.push(serviceEntry);
                    }
                  });
                }
              } catch (error) {
                console.error('Error parsing service data:', error);
              }
            }
          });
        });

        setSelectedServices(services);
        servicesLoadedRef.current = true;
      }
    }
  }, [userInputs]);

  // Show loading state if data is being fetched
  if (isLoading || isLoadingRedux) {
    return (
      <>
        <GradiantHeader title="Other Passwords" showAvatar={true} />
        <div className="p-4 text-center">Loading your answers...</div>
      </>
    );
  }

  const currentStepQuestions = steps[step] || [];

  const addService = () => {
    if (!selectedService) return;
    
    if (selectedService === 'Other' && !customServiceName.trim()) {
      return;
    }

    // Check for duplicates
    const isDuplicate = selectedServices.some(service => {
      if (selectedService === 'Other' && customServiceName) {
        return service.serviceName === 'Other' && service.customServiceName === customServiceName;
      }
      return service.serviceName === selectedService;
    });

    if (isDuplicate) {
      const displayName = selectedService === 'Other' && customServiceName ? customServiceName : selectedService;
      setDuplicateServiceError(`${displayName} is already added`);
      
      setTimeout(() => {
        setDuplicateServiceError('');
      }, 3000);
      
      return;
    }

    const newService: PasswordEntry = {
      serviceName: selectedService,
      accounts: [{ username: '', password: '' }]
    };

    if (selectedService === 'Other' && customServiceName) {
      newService.customServiceName = customServiceName;
      newService.displayName = customServiceName;
    } else {
      newService.displayName = selectedService;
    }

    setSelectedServices([...selectedServices, newService]);
    setSelectedService('');
    setCustomServiceName('');
    setDuplicateServiceError('');
  };

  const removeService = (index: number) => {
    const newServices = selectedServices.filter((_, i) => i !== index);
    setSelectedServices(newServices);
  };

  const addAccount = (serviceIndex: number) => {
    const newServices = [...selectedServices];
    newServices[serviceIndex].accounts.push({ username: '', password: '' });
    setSelectedServices(newServices);
  };

  const removeAccount = (serviceIndex: number, accountIndex: number) => {
    const newServices = [...selectedServices];
    if (newServices[serviceIndex].accounts.length > 1) {
      newServices[serviceIndex].accounts.splice(accountIndex, 1);
      setSelectedServices(newServices);
    }
  };

  const updateAccount = (serviceIndex: number, accountIndex: number, field: 'username' | 'password', value: string) => {
    const newServices = [...selectedServices];
    newServices[serviceIndex].accounts[accountIndex][field] = value;
    setSelectedServices(newServices);
  };

  const togglePasswordVisibility = (serviceIndex: number, accountIndex: number) => {
    const key = `${serviceIndex}-${accountIndex}`;
    setShowPasswords(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  return (
    <>
      <GradiantHeader title="Other Passwords" showAvatar={true} />
      <div className="p-4">
        {/* Error message */}
        {(formError || reduxError) && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{formError || reduxError}</AlertDescription>
          </Alert>
        )}

        {/* Form */}
        <Formik
          initialValues={Object.keys(savedAnswers).length > 0 ? savedAnswers : {}}
          validationSchema={validationSchema}
          enableReinitialize={true}
          onSubmit={async (values, { setSubmitting }) => {
            try {
              if (!user || !user.id) {
                setError('You must be logged in to save answers');
                return;
              }

              // Prepare answers for saving (same structure as web version)
              const answersBySection = [{
                originalSectionId: '1204A',
                isCompleted: true,
                answers: [
                  // Yes/No answer
                  {
                    index: 0,
                    originalQuestionId: YESNO_QUESTION_ID,
                    question: 'Do you want to save any other passwords?',
                    type: 'boolean',
                    answer: values[YESNO_QUESTION_ID]
                  },
                  // Service selection answer (only if they answered yes)
                  ...(values[YESNO_QUESTION_ID] === 'yes' ? [{
                    index: 1,
                    originalQuestionId: DROPDOWN_QUESTION_ID,
                    question: 'Select the services/accounts you want to store passwords for:',
                    type: 'choice',
                    answer: JSON.stringify(selectedServices)
                  }] : [])
                ]
              }];

              const userData = {
                userId: user.id,
                categoryId: generateObjectId(),
                originalCategoryId: '12',
                subCategoryId: generateObjectId(),
                originalSubCategoryId: '1204A',
                answersBySection: answersBySection
              };

              if (existingInputId) {
                await dispatch(updateUserInput({
                  id: existingInputId,
                  userData
                })).unwrap();
              } else {
                await dispatch(saveUserInput(userData)).unwrap();
              }
              
              // Navigate to review page after successful save
              navigate('/category/passwords/review');
              setSubmitting(false);
            } catch (err) {
              setError("Failed to save your answers. Please try again.");
              setSubmitting(false);
            }
          }}
        >
          {({ values, isSubmitting, errors, touched, setTouched, validateForm, handleSubmit }) => {
            const visibleQuestions = currentStepQuestions.filter(q => {
              if (!q.dependsOn) return true;
              return values[q.dependsOn.questionId] === q.dependsOn.value;
            });

            const handleNext = async () => {
              if (step === 0 && values[YESNO_QUESTION_ID] === "no") {
                handleSubmit();
              } else {
                setStep(s => s + 1);
              }
            };

            const handleSave = async (e: React.FormEvent) => {
              if (step === 0 && values[YESNO_QUESTION_ID] === "no") {
                handleSubmit();
              } else {
                handleSubmit();
              }
            };

            return (
              <Form>
                <div className="bg-gray-50 p-5 rounded-xl shadow-sm border mb-4">
                  <div className="flex items-center justify-between">
                    <p className="text-lg font-semibold">
                      Passwords: <span className="text-[#2BCFD5]">Other Passwords</span>
                    </p>
                    <CircularProgress
                      value={step + 1}
                      max={values[YESNO_QUESTION_ID] === "no" ? 1 : steps.length}
                      size={40}
                      stroke={3}
                      color="#2BCFD5"
                    />
                  </div>
                </div>

                {/* Step 0: Yes/No Questions */}
                {step === 0 && (
                  <ScrollToQuestion questions={visibleQuestions}>
                    {(questionRefs) => (
                      <div className="space-y-4 mt-8 bg-gray-50 p-5 rounded-xl shadow-sm border">
                        {visibleQuestions.map(q => (
                          <div key={q.id} ref={(el) => { questionRefs[q.id] = el; }}>
                            <label className="block font-medium text-gray-700 mb-2">
                              {q.text}
                            </label>
                            {q.type === "yesno" ? (
                              <div className="flex space-x-4">
                                {["yes", "no"].map(option => (
                                  <label
                                    key={option}
                                    className={
                                      "flex-1 py-2 px-4 border rounded-xl text-center cursor-pointer " +
                                      (values[q.id] === option
                                        ? "bg-[#2BCFD5] text-white border-[#2BCFD5]"
                                        : "bg-gray-50 hover:bg-[#25b6bb] hover:text-white")
                                    }
                                    style={{ transition: "all 0.2s" }}
                                  >
                                    <Field type="radio" name={q.id} value={option} className="hidden" />
                                    {option.charAt(0).toUpperCase() + option.slice(1)}
                                  </label>
                                ))}
                              </div>
                            ) : (
                              <Field
                                name={q.id}
                                type="text"
                                className="w-full border rounded-lg px-3 py-2"
                              />
                            )}
                            <ErrorMessage name={q.id} component="div" className="text-red-500 text-sm mt-1" />
                          </div>
                        ))}
                      </div>
                    )}
                  </ScrollToQuestion>
                )}

                {/* Step 1: Service Selection and Password Details */}
                {step === 1 && (
                  <div className="space-y-4 mt-8">
                    {/* Service Selection */}
                    <div className="bg-white rounded-xl p-4 mb-6 shadow-sm">
                      <Label className="text-base font-medium text-gray-900 mb-4 block">
                        Select services to store passwords for:
                      </Label>
                      
                      <div className="space-y-4">
                        <Select value={selectedService} onValueChange={setSelectedService} key="service-select">
                          <SelectTrigger>
                            <SelectValue placeholder="Select a service..." />
                          </SelectTrigger>
                          <SelectContent>
                            {dropdownOptions.map((option: any) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>

                        {selectedService === 'Other' && (
                          <Input
                            placeholder="Enter custom service name..."
                            value={customServiceName}
                            onChange={(e) => setCustomServiceName(e.target.value)}
                          />
                        )}

                        <Button
                          type="button"
                          onClick={addService}
                          disabled={!selectedService || (selectedService === 'Other' && !customServiceName.trim())}
                          className="w-full bg-[#2BCFD5] hover:bg-[#25b5ba] text-white"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Add Service
                        </Button>

                        {duplicateServiceError && (
                          <p className="text-red-500 text-sm">{duplicateServiceError}</p>
                        )}
                      </div>
                    </div>

                    {/* Selected Services */}
                    {selectedServices.length > 0 && (
                      <div className="space-y-4 mb-6">
                        <h3 className="text-lg font-medium text-gray-900">Your Password Entries:</h3>
                        {selectedServices.map((service, serviceIndex) => (
                          <div key={`${service.serviceName}-${serviceIndex}`} className="bg-white rounded-xl p-4 shadow-sm" id={`service-${serviceIndex}`}>
                            <div className="flex justify-between items-center mb-4">
                              <h4 className="text-lg font-medium text-gray-900">{service.displayName}</h4>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => removeService(serviceIndex)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                            
                            <div className="space-y-3">
                              {service.accounts.map((account, accountIndex) => (
                                <div key={accountIndex} className="p-3 bg-gray-50 rounded-lg">
                                  <div className="space-y-3">
                                    <div>
                                      <Label className="text-sm font-medium text-gray-700">Username/Email</Label>
                                      <Input
                                        placeholder="Enter username or email..."
                                        value={account.username}
                                        onChange={(e) => updateAccount(serviceIndex, accountIndex, 'username', e.target.value)}
                                      />
                                    </div>
                                    <div>
                                      <Label className="text-sm font-medium text-gray-700">Password</Label>
                                      <div className="relative">
                                        <Input
                                          type={showPasswords[`${serviceIndex}-${accountIndex}`] ? "text" : "password"}
                                          placeholder="Enter password..."
                                          value={account.password}
                                          onChange={(e) => updateAccount(serviceIndex, accountIndex, 'password', e.target.value)}
                                          className="pr-10"
                                        />
                                        <button
                                          type="button"
                                          onClick={() => togglePasswordVisibility(serviceIndex, accountIndex)}
                                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                                        >
                                          {showPasswords[`${serviceIndex}-${accountIndex}`] ? (
                                            <EyeOff className="w-4 h-4" />
                                          ) : (
                                            <Eye className="w-4 h-4" />
                                          )}
                                        </button>
                                      </div>
                                    </div>
                                    {service.accounts.length > 1 && (
                                      <Button
                                        type="button"
                                        variant="outline"
                                        size="sm"
                                        onClick={() => removeAccount(serviceIndex, accountIndex)}
                                        className="text-red-600 hover:text-red-700 w-full"
                                      >
                                        <Trash2 className="w-4 h-4 mr-2" />
                                        Remove Account
                                      </Button>
                                    )}
                                  </div>
                                </div>
                              ))}
                              
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => addAccount(serviceIndex)}
                                className="w-full"
                              >
                                <Plus className="w-4 h-4 mr-2" />
                                Add Another Account
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                <div className="mt-6 flex justify-between items-center">
                  <button
                    type="button"
                    onClick={() => setStep(s => s - 1)}
                    disabled={step === 0}
                    className="text-[#2BCFD5] underline disabled:opacity-50"
                  >
                    ← Back
                  </button>
                  {step < steps.length - 1 ? (
                    <button
                      type="button"
                      onClick={handleNext}
                      className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb]"
                    >
                      Next →
                    </button>
                  ) : (
                    <button
                      type="button"
                      onClick={handleSave}
                      disabled={isSubmitting}
                      className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb]"
                    >
                      {isSubmitting ? 'Saving...' : 'Save'}
                    </button>
                  )}
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
      <Footer />
    </>
  );
}
