import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { Question } from './FormFields';

interface ScrollToQuestionProps {
  questions: Question[];
  children: (refs: Record<string, HTMLDivElement | null>) => React.ReactNode;
}

const ScrollToQuestion = ({ questions = [], children }: ScrollToQuestionProps) => {
  const location = useLocation();
  const questionRefs = useRef<Record<string, HTMLDivElement | null>>({});

  const queryParams = new URLSearchParams(location.search);
  const targetQuestionId = queryParams.get('questionId');

  useEffect(() => {
    if (targetQuestionId && questionRefs.current[targetQuestionId]) {
      setTimeout(() => {
        questionRefs.current[targetQuestionId]?.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });

        const element = questionRefs.current[targetQuestionId];
        if (element) {
          element.classList.add('bg-yellow-50');
          element.classList.add('transition-colors');
          element.classList.add('duration-1000');

          setTimeout(() => {
            element.classList.remove('bg-yellow-50');
          }, 3000);
        }
      }, 500);
    }
  }, [targetQuestionId, questions]);

  return children(questionRefs.current);
};

export default ScrollToQuestion;
