interface PurchasedPlanData {
  sessionId: string;
  planType: string;
  planId?: string;
  planName?: string;
  planPrice?: number;
  paymentStatus: string;
  timestamp: number;
}

class SessionStorageManager {
  private static readonly PURCHASED_PLAN_KEY = 'heirkey_purchased_plan';
  private static readonly EXPIRY_TIME = 30 * 60 * 1000; // 30 minutes

  /**
   * Store purchased plan data in session storage
   */
  static storePurchasedPlan(data: Omit<PurchasedPlanData, 'timestamp'>): void {
    const planData: PurchasedPlanData = {
      ...data,
      timestamp: Date.now()
    };
    
    try {
      sessionStorage.setItem(this.PURCHASED_PLAN_KEY, JSON.stringify(planData));
      console.log('Purchased plan data stored:', planData);
    } catch (error) {
      console.error('Failed to store purchased plan data:', error);
    }
  }

  /**
   * Retrieve purchased plan data from session storage
   */
  static getPurchasedPlan(): PurchasedPlanData | null {
    try {
      const stored = sessionStorage.getItem(this.PURCHASED_PLAN_KEY);
      if (!stored) return null;

      const planData: PurchasedPlanData = JSON.parse(stored);
      
      // Check if data has expired
      if (Date.now() - planData.timestamp > this.EXPIRY_TIME) {
        this.clearPurchasedPlan();
        return null;
      }

      return planData;
    } catch (error) {
      console.error('Failed to retrieve purchased plan data:', error);
      return null;
    }
  }

  /**
   * Clear purchased plan data from session storage
   */
  static clearPurchasedPlan(): void {
    try {
      sessionStorage.removeItem(this.PURCHASED_PLAN_KEY);
      console.log('Purchased plan data cleared');
    } catch (error) {
      console.error('Failed to clear purchased plan data:', error);
    }
  }

  /**
   * Check if user has a valid purchased plan in session
   */
  static hasPurchasedPlan(): boolean {
    return this.getPurchasedPlan() !== null;
  }

  /**
   * Update payment status of stored plan
   */
  static updatePaymentStatus(status: string): void {
    const planData = this.getPurchasedPlan();
    if (planData) {
      planData.paymentStatus = status;
      planData.timestamp = Date.now(); // Refresh timestamp
      this.storePurchasedPlan(planData);
    }
  }
}

export default SessionStorageManager;
export type { PurchasedPlanData };
