import { useState, useEffect } from 'react';
import CategoryReviewPage from '@/mobile/components/category/CategoryReviewPage';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  UserInput as ReduxUserInput
} from '@/store/slices/insuranceSlice';
import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import insuranceData from '@/data/insurance.json';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { Button } from '@/components/ui/button';

// Define interfaces for the data structure
interface Answer {
  index: number;
  questionId?: string;
  originalQuestionId: string;
  question: string;
  type: string;
  answer: string;
}

interface SectionAnswers {
  originalSectionId: string;
  isCompleted: boolean;
  answers: Answer[];
}

// Map section IDs to their routes
const subcategoryRoutes: Record<string, string> = {
  '401A': 'home',
  '401B': 'medical',
  '401C': 'life',
};

// Map question IDs to their section IDs
const questionToSectionMap: Record<string, string> = {};

// Initialize the question to section mapping
Object.entries(insuranceData).forEach(([, questions]) => {
  questions.forEach((question: any) => {
    if (question.sectionId) {
      questionToSectionMap[question.id] = question.sectionId;
    }
  });
});



export default function InsuranceReviewPage() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [topics, setTopics] = useState<Array<{
    id: string;
    title: string;
    subtitle?: string;
    data: string;
    onEdit: () => void;
  }>>([]);
  const [error, setError] = useState<string | null>(null);

  // Get data from Redux store
  const userInputs = useAppSelector((state: any) => state.insurance.userInputs) as ReduxUserInput[];
  const isLoading = useAppSelector((state: any) => state.insurance.loading);

  // Fallback user info if not authenticated
  const userInfo = {
    name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
    email: user?.email || '<EMAIL>',
    avatar: user?.image || avatar
  };

  // Fetch user inputs when component mounts using owner ID
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in InsuranceReviewPage component');
          }
        } catch (error) {
          console.error('Error fetching owner ID in InsuranceReviewPage component:', error);
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  // Helper function to get section name from ID
  const getSectionName = (sectionId: string): string => {
    switch (sectionId) {
      case '401A': return 'Home Insurance';
      case '401B': return 'Medical Insurance';
      case '401C': return 'Life Insurance';
      default: return 'Unknown';
    }
  };

  // Helper function to get section ID from label
  const getSectionIdFromLabel = (label: string): string | null => {
    switch (label) {
      case 'Home Insurance': return '401A';
      case 'Medical Insurance': return '401B';
      case 'Life Insurance': return '401C';
      default: return null;
    }
  };

  // Process user inputs to create topics for review page
  useEffect(() => {
    if (!isLoading && userInputs.length > 0) {
      // Transform the data for the review page
      const allTopics: Array<{
        id: string;
        title: string;
        subtitle?: string;
        data: string;
        onEdit: () => void;
      }> = [];

      // Process user inputs from the single insurance record (web version approach)
      userInputs.forEach((userInput: ReduxUserInput) => {
        // Check if this is the insurance category with subcategory '401'
        if (userInput.originalCategoryId === '8' && userInput.originalSubCategoryId === '401') {

          userInput.answersBySection.forEach((section) => {
            section.answers.forEach((answer) => {
              // Find the original question from our data
              const questionId = answer.originalQuestionId;
              const sectionId = questionToSectionMap[questionId];
              const allQuestions = insuranceData['401'];
              const questionData = allQuestions?.find((q: any) => q.id === questionId);

              if (questionData) {
                let displayAnswer = answer.answer;

                // Handle choice type answers
                if (questionData.type === 'choice' || questionData.type === 'dropdown') {
                  // If the answer is "Other (Please List)", look for the other provider name
                  if (answer.answer === "Other (Please List)" || answer.answer === "Other") {
                    const otherAnswer = section.answers.find(a => a.originalQuestionId === `${answer.originalQuestionId}_other`);
                    if (otherAnswer) {
                      displayAnswer = `Other: ${otherAnswer.answer}`;
                    }
                  }
                }

                allTopics.push({
                  id: questionId,
                  title: questionData.text,
                  subtitle: `Section: ${getSectionName(sectionId)}`,
                  data: displayAnswer,
                  onEdit: () => {
                    // Navigate to the appropriate section page with question ID as a parameter
                    const route = subcategoryRoutes[sectionId];
                    if (route) {
                      navigate(`/category/insurance/${route}?questionId=${questionId}`);
                    }
                  }
                });
              }
            });
          });
        }
      });

      setTopics(allTopics);
    }
  }, [userInputs, isLoading, navigate]);

  if (isLoading) {
    return (
      <div className="p-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2BCFD5] mx-auto mb-4"></div>
          <p>Loading your answers...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <Alert>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!user || !user.id) {
    return (
      <div className="p-4">
        <Alert>
          <AlertDescription>You must be logged in to view your answers</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (userInputs.length === 0 && !isLoading) {
    return (
      <div className="p-4">
        <Alert>
          <AlertDescription>No insurance answers found. Please complete some questions first.</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <CategoryReviewPage
      categoryTitle="Insurance"
      infoTitle="How to edit your information"
      infoDescription="Now, you are about to enter details about your insurance policies, preferences, and essential information to be passed on to your family members. Each section has several questions. Fill out as much as you can/like. You can always come back to fill out more information later."
      topics={topics}
      onPrint={() => window.print()}
      afterTopics={
        <Button
          onClick={() => navigate('/category/financialplans')}
          className="px-8 py-3 bg-[#2BCFD5] text-white rounded-md hover:bg-[#1F4168] transition-colors duration-200 shadow-md font-semibold text-md mt-1 mb-1"
        >
          Back to Finance Plans
        </Button>
      }
    />
  );
}
