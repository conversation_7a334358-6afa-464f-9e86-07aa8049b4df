import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store';
import { fetchPricingPlans } from '@/store/slices/subscriptionSlice';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Check, Crown, Zap, Shield, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import stripeService from '@/services/stripeService';
import { PricingPlan } from '@/types/subscription';
import { z } from 'zod';

const emailSchema = z.string().email('Please enter a valid email address');

export default function UnauthenticatedSubscribePage() {
  const [billing, setBilling] = useState("monthly");
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const [selectedPlan, setSelectedPlan] = useState<PricingPlan | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  
  const dispatch = useDispatch<AppDispatch>();
  const { plans, loading, error } = useSelector((state: RootState) => state.subscription);
  const { toast } = useToast();

  useEffect(() => {
    dispatch(fetchPricingPlans());
  }, [dispatch]);

  const validateEmail = (emailValue: string) => {
    try {
      emailSchema.parse(emailValue);
      setEmailError('');
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        setEmailError(error.errors[0].message);
      }
      return false;
    }
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEmail(value);
    if (value) {
      validateEmail(value);
    } else {
      setEmailError('');
    }
  };

  const handleSubscribe = async (plan: PricingPlan) => {
    if (!validateEmail(email)) {
      toast({
        title: "Invalid Email",
        description: "Please enter a valid email address to continue.",
        variant: "destructive",
      });
      return;
    }

    setSelectedPlan(plan);
    setIsProcessing(true);

    try {
      // Create checkout session with email for unauthenticated users
      const session = await stripeService.createCheckoutSession(plan.type, email, true);
      
      if (session.url) {
        // Redirect to Stripe checkout
        window.location.href = session.url;
      } else {
        throw new Error('No checkout URL received');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start checkout process';
      toast({
        title: "Checkout Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const getPlanIcon = (planType: string) => {
    switch (planType) {
      case 'temporary_key':
        return <Shield className="h-8 w-8 text-blue-600" />;
      case 'spare_key':
        return <Zap className="h-8 w-8 text-purple-600" />;
      case 'all_access_key':
        return <Crown className="h-8 w-8 text-yellow-600" />;
      default:
        return <Shield className="h-8 w-8 text-gray-600" />;
    }
  };

  const getPlanName = (planType: string) => {
    switch (planType) {
      case 'temporary_key':
        return 'Temporary Key';
      case 'spare_key':
        return 'Spare Key';
      case 'all_access_key':
        return 'All Access Key';
      default:
        return planType;
    }
  };

  const isPopularPlan = (planType: string) => {
    return planType === 'spare_key'; // Make spare_key the popular plan
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading plans: {error}</p>
          <Button onClick={() => dispatch(fetchPricingPlans())}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      {/* Header */}
      <div className="bg-[#1F4168] text-white pt-12 pb-8 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-4xl font-bold mb-4">Choose Your HeirKey Plan</h1>
          <p className="text-xl text-gray-200 mb-6">
            To get started, enter your email and select a plan.
          </p>
          
          {/* Email Input */}
          <div className="max-w-md mx-auto">
            <Input
              type="email"
              placeholder="Enter your email address"
              value={email}
              onChange={handleEmailChange}
              className={`w-full p-3 text-lg text-white ${emailError ? 'border-red-500' : ''}`}
            />
            {emailError && (
              <p className="text-red-300 text-sm mt-2">{emailError}</p>
            )}
          </div>
        </div>
      </div>

      {/* Plans */}
      <div className="max-w-7xl mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {plans.map((plan) => (
            <Card
              key={plan._id}
              className={`relative transition-all duration-300 hover:shadow-lg ${
                isPopularPlan(plan.type) ? 'ring-2 ring-[#2BCFD5] scale-105' : ''
              }`}
            >
              {isPopularPlan(plan.type) && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-[#2BCFD5] text-white">
                  Most Popular
                </Badge>
              )}

              <CardHeader className="text-center pb-4">
                <div className="flex justify-center mb-4">
                  {getPlanIcon(plan.type)}
                </div>
                <CardTitle className="text-2xl font-bold">{getPlanName(plan.type)}</CardTitle>
                <p className="text-gray-600">{plan.tagline}</p>
                <div className="mt-4">
                  <span className="text-4xl font-bold text-[#1F4168]">
                    {plan.price === 0 ? 'Free' :
                     plan.price !== undefined ? `$${plan.price}` : plan.displayPrice || 'Price not available'}
                  </span>
                  {plan.price > 0 && plan.billingCycle && (
                    <span className="text-gray-600">/{plan.billingCycle}</span>
                  )}
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <ul className="space-y-3 mb-6">
                  {plan.features?.map((feature, index) => (
                    <li key={index} className="flex items-center gap-3">
                      <Check className="h-5 w-5 text-green-500 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Button
                  onClick={() => handleSubscribe(plan)}
                  disabled={!email || !!emailError || isProcessing}
                  className={`w-full py-3 text-lg font-semibold transition-all duration-300 ${
                    isPopularPlan(plan.type)
                      ? 'bg-[#2BCFD5] hover:bg-[#2BCFD5]/90 text-white'
                      : 'bg-[#1F4168] hover:bg-[#1F4168]/90 text-white'
                  }`}
                >
                  {isProcessing && selectedPlan?._id === plan._id ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    `Choose ${getPlanName(plan.type)}`
                  )}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="text-center mt-12">
          <p className="text-gray-600">
            After selecting a plan, you'll complete payment and then register your account.
          </p>
        </div>
      </div>
    </div>
  );
}
