import React from 'react';
import { Field, ErrorMessage } from 'formik';

export interface Question {
  id: string;
  text: string;
  type: 'text' | 'choice' | 'boolean' | 'yesno' | 'dropdown' | 'textarea' | 'email' | 'password';
  required: boolean;
  sectionId: string;
  options?: Array<{
    value: string;
    label: string;
  }>;
  order: number;
  dependsOn?: {
    questionId: string;
    value: string;
  };
}

export const buildValidationSchema = (questions: Question[], Yup: any) => {
  const schema: Record<string, any> = {};

  questions.forEach(question => {
    let fieldSchema;

    switch (question.type) {
      case 'text':
      case 'email':
      case 'password':
        fieldSchema = Yup.string();
        break;
      case 'textarea':
        fieldSchema = Yup.string();
        break;
      case 'choice':
      case 'dropdown':
        fieldSchema = Yup.string();
        break;
      case 'boolean':
      case 'yesno':
        fieldSchema = Yup.string();
        break;
      default:
        fieldSchema = Yup.string();
    }

    // For passwords category, all fields are optional by default
    if (question.required) {
      fieldSchema = fieldSchema.required(`${question.text} is required`);
    }

    schema[question.id] = fieldSchema;
  });

  return Yup.object().shape(schema);
};

export const generateInitialValues = (questions: Question[]) => {
  const values: Record<string, any> = {};
  questions.forEach(question => {
    values[question.id] = '';
  });
  return values;
};

interface QuestionItemProps {
  question: Question;
  values: Record<string, any>;
}

export const QuestionItem: React.FC<QuestionItemProps> = ({ question, values }) => {
  // Check if question should be shown based on dependencies
  if (question.dependsOn) {
    const dependentValue = values[question.dependsOn.questionId];
    if (dependentValue !== question.dependsOn.value) {
      return null;
    }
  }

  const renderField = () => {
    switch (question.type) {
      case 'text':
      case 'email':
      case 'password':
        return (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {question.text}
              {question.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <Field
              name={question.id}
              type={question.type}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <ErrorMessage name={question.id} component="div" className="text-red-500 text-sm mt-1" />
          </div>
        );

      case 'textarea':
        return (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {question.text}
              {question.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <Field
              as="textarea"
              name={question.id}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <ErrorMessage name={question.id} component="div" className="text-red-500 text-sm mt-1" />
          </div>
        );

      case 'choice':
        return (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {question.text}
              {question.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <div className="grid grid-cols-2 gap-2">
              {question.options?.map((option) => (
                <label key={option.value} className="flex items-center">
                  <Field
                    type="radio"
                    name={question.id}
                    value={option.value}
                    className="mr-2"
                  />
                  <span className="text-sm">{option.label}</span>
                </label>
              ))}
            </div>
            <ErrorMessage name={question.id} component="div" className="text-red-500 text-sm mt-1" />
          </div>
        );

      case 'dropdown':
        return (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {question.text}
              {question.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <Field
              as="select"
              name={question.id}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select an option</option>
              {question.options?.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Field>
            <ErrorMessage name={question.id} component="div" className="text-red-500 text-sm mt-1" />
          </div>
        );

      case 'boolean':
      case 'yesno':
        return (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {question.text}
              {question.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <div className="flex gap-4">
              <label className="flex items-center">
                <Field
                  type="radio"
                  name={question.id}
                  value="yes"
                  className="mr-2"
                />
                <span className="text-sm">Yes</span>
              </label>
              <label className="flex items-center">
                <Field
                  type="radio"
                  name={question.id}
                  value="no"
                  className="mr-2"
                />
                <span className="text-sm">No</span>
              </label>
            </div>
            <ErrorMessage name={question.id} component="div" className="text-red-500 text-sm mt-1" />
          </div>
        );

      default:
        return null;
    }
  };

  return renderField();
};

// Helper function to handle dependent questions
export const handleDependentAnswers = (values: Record<string, any>, questions: Question[]) => {
  const updatedValues = { ...values };

  questions.forEach((question) => {
    if (question.dependsOn) {
      const dependentValue = values[question.dependsOn.questionId];
      if (dependentValue !== question.dependsOn.value) {
        // Clear the value if the dependency is not met
        updatedValues[question.id] = '';
      }
    }
  });

  return updatedValues;
};

// Helper function to map frontend question types to backend-accepted enum values
export const mapQuestionTypeForBackend = (type: string): string => {
  switch (type) {
    case 'yesno':
      return 'boolean';
    case 'dropdown':
      return 'choice';
    case 'password':
    case 'email':
      return 'text';
    default:
      return type;
  }
};
