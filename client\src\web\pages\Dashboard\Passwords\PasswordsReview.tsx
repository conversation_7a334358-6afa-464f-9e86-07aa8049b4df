import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Avatar } from '@radix-ui/react-avatar';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import { useAuth } from '@/contexts/AuthContext';
import passwordsData from '@/data/Passwords.json';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  selectUserInputs,
  selectLoading,
  selectError,
  UserInput
} from '@/store/slices/passwordsSlice';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { createUserInfo } from '@/utils/avatarUtils';

// Map section IDs to their routes
const sectionRoutes: Record<string, string> = {
  '1204A': '/category/passwords/otherpasswords',
};

// Map question IDs to their section IDs
const questionToSectionMap: Record<string, string> = {};
Object.entries(passwordsData).forEach(([categoryId, questions]) => {
  questions.forEach((question: any) => {
    if (question.sectionId) {
      questionToSectionMap[question.id] = question.sectionId;
    }
  });
});

const PasswordsReview = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [topics, setTopics] = useState<Array<{
    id: string;
    title: string;
    subtitle?: string;
    data: string;
    onEdit: () => void;
  }>>([]);

  const userInputs = useAppSelector(selectUserInputs);
  const isLoading = useAppSelector(selectLoading);
  const error = useAppSelector(selectError);

  // Check authentication and redirect if needed
  useEffect(() => {
    if (!user || !user.id) {
      console.log('PasswordsReview: No user found, redirecting to get-started');
      navigate('/auth/get-started');
      return;
    }
    console.log('PasswordsReview: User authenticated, proceeding');
  }, [user, navigate]);

  const userInfo = {
    name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
    email: user?.email || '<EMAIL>',
    avatar: createUserInfo(user).avatar
  };

  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in PasswordsReview component');
          }
        } catch (error) {
          console.error('Error fetching owner ID in PasswordsReview component:', error);
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  useEffect(() => {
    if (!isLoading && userInputs.length > 0) {
      const allTopics: Array<{
        id: string;
        title: string;
        subtitle?: string;
        data: string;
        onEdit: () => void;
      }> = [];

      // Filter userInputs to only include passwords category (originalCategoryId: '12')
      // and get the most recent one to avoid duplicates
      const passwordUserInputs = userInputs.filter((input: UserInput) =>
        input.originalCategoryId === '12'
      );

      // Sort by creation date and take the most recent one
      // Use _id as fallback since MongoDB ObjectIds contain timestamp information
      const sortedInputs = passwordUserInputs.sort((a, b) => {
        // Try to use createdAt if available (from backend timestamps)
        const aTime = (a as any).createdAt ? new Date((a as any).createdAt).getTime() : 0;
        const bTime = (b as any).createdAt ? new Date((b as any).createdAt).getTime() : 0;

        // If both have createdAt, use that
        if (aTime && bTime) {
          return bTime - aTime; // Most recent first
        }

        // Fallback to _id comparison (MongoDB ObjectIds contain timestamp)
        if (a._id && b._id) {
          return a._id > b._id ? -1 : 1; // Most recent first
        }

        // Final fallback - keep original order
        return 0;
      });

      // Only process the most recent password input to avoid duplicates
      const latestPasswordInput = sortedInputs[0];

      if (latestPasswordInput) {
        const userInput = latestPasswordInput;

        // Track processed questions to avoid duplicates
        const processedQuestions = new Set<string>();

        userInput.answersBySection.forEach((section) => {
          // First, handle yes/no questions to show them first
          section.answers.forEach((answer) => {
            // Find the original question from our data
            const questionId = answer.originalQuestionId;

            // Skip if we've already processed this question
            if (processedQuestions.has(questionId)) {
              return;
            }

            const allQuestions = passwordsData['1204'];
            const questionData = allQuestions?.find((q: any) => q.id === questionId);

            if (questionData && questionData.type === 'yesno') {
              // Mark this question as processed
              processedQuestions.add(questionId);

              let displayAnswer = answer.answer;

              // Format yes/no answers for better display
              if (answer.answer === 'yes') {
                displayAnswer = 'Yes';
              } else if (answer.answer === 'no') {
                displayAnswer = 'No';
              }

              allTopics.push({
                id: questionId,
                title: questionData.text,
                subtitle: 'Password Preference',
                data: displayAnswer,
                onEdit: () => {
                  const route = sectionRoutes['1204A'];
                  console.log('PasswordsReview: Edit button clicked for question:', questionId);
                  console.log('PasswordsReview: Route:', route);
                  if (route) {
                    console.log('PasswordsReview: Navigating to:', `${route}?questionId=${questionId}&fromReview=1`);
                    navigate(`${route}?questionId=${questionId}&fromReview=1`);
                  }
                }
              });
            }
          });

          // Then handle service data (only if they answered yes)
          const yesNoAnswer = section.answers.find(a => a.originalQuestionId === 'p2');
          if (yesNoAnswer?.answer === 'yes') {
            // Special handling for Other Passwords (section 1204A)
            if (section.originalSectionId === '1204A') {
              // Find the main dropdown selection
              const dropdownAnswer = section.answers.find(a => a.originalQuestionId === 'p1');
              
              if (dropdownAnswer?.answer) {
                try {
                  // Parse the JSON structure for services
                  const servicesData = JSON.parse(dropdownAnswer.answer);
                  
                  if (Array.isArray(servicesData)) {
                    // Handle new structure with multiple services and accounts
                    servicesData.forEach((service: any, index: number) => {
                      if (service.serviceName && service.accounts && Array.isArray(service.accounts)) {
                        const serviceName = service.serviceName;
                        const accounts = service.accounts.filter((account: any) => 
                          account.username || account.password
                        );
                        
                        if (accounts.length > 0) {
                          // Format accounts for display in the requested format
                          const displayName = service.displayName || service.serviceName;
                          let accountsDisplay = `service name: ${displayName}\n`;
                          accounts.forEach((account: any, accountIndex: number) => {
                            accountsDisplay += `Account ${accountIndex + 1}\n`;
                            const parts = [];
                            if (account.username) {
                              parts.push(`username: ${account.username}`);
                            }
                            if (account.password) {
                              parts.push(`password: ${account.password}`);
                            }
                            accountsDisplay += parts.join('  |  ') + '\n';
                          });
                          
                          allTopics.push({
                            id: `service-${displayName}-${index}`,
                            title: displayName,
                            subtitle: `${accounts.length} account${accounts.length !== 1 ? 's' : ''}`,
                            data: accountsDisplay.trim(),
                            onEdit: () => {
                              const route = sectionRoutes['1204A'];
                              console.log('PasswordsReview: Navigating to service with index:', index);
                              console.log('PasswordsReview: Route:', route);
                              if (route) {
                                // Pass the service index for highlighting
                                navigate(`${route}?serviceIndex=${index}&fromReview=1`);
                              }
                            }
                          });
                        } else {
                          // Service with no valid accounts
                          const displayName = service.displayName || service.serviceName;
                          allTopics.push({
                            id: `service-${displayName}-${index}`,
                            title: displayName,
                            subtitle: 'Password Service',
                            data: 'No credentials provided',
                            onEdit: () => {
                              const route = sectionRoutes['1204A'];
                              if (route) {
                                // Pass the service index for highlighting
                                navigate(`${route}?serviceIndex=${index}&fromReview=1`);
                              }
                            }
                          });
                        }
                      }
                    });
                  } else {
                    // Handle old single service format
                    const selectedService = dropdownAnswer.answer;
                    
                    if (selectedService === 'Other') {
                      // Handle "Other" service with custom name
                      const serviceNameAnswer = section.answers.find(a => a.originalQuestionId === 'p2');
                      const serviceName = serviceNameAnswer?.answer || 'Custom Service';
                      
                      allTopics.push({
                        id: 'other-service-custom',
                        title: serviceName,
                        subtitle: 'Custom Service',
                        data: 'Service added manually',
                        onEdit: () => {
                          const route = sectionRoutes['1204A'];
                          if (route) {
                            // For old format, we don't have a specific index, so just navigate
                            navigate(`${route}?fromReview=1`);
                          }
                        }
                      });
                    } else {
                      // Handle specific service selection (old format)
                      const usernameAnswer = section.answers.find(a => 
                        a.originalQuestionId === 'p3' || // Username field
                        a.originalQuestionId === 'p5' || // Additional username fields
                        a.originalQuestionId === 'p7'    // More username fields
                      );
                      
                      const passwordAnswer = section.answers.find(a => 
                        a.originalQuestionId === 'p4' || // Password field
                        a.originalQuestionId === 'p6' || // Additional password fields
                        a.originalQuestionId === 'p8'    // More password fields
                      );
                      
                      const credentials = [];
                      if (usernameAnswer?.answer) {
                        credentials.push(`Username: ${usernameAnswer.answer}`);
                      }
                      if (passwordAnswer?.answer) {
                        credentials.push(`Password: ${passwordAnswer.answer}`);
                      }
                      
                      allTopics.push({
                        id: `service-${selectedService}`,
                        title: selectedService,
                        subtitle: 'Password Service',
                        data: credentials.length > 0 ? credentials.join(' | ') : 'No credentials provided',
                        onEdit: () => {
                          const route = sectionRoutes['1204A'];
                          console.log('PasswordsReview: Navigating to service (old format)');
                          console.log('PasswordsReview: Route:', route);
                          if (route) {
                            // For old format, we don't have a specific index, so just navigate
                            navigate(`${route}?fromReview=1`);
                          }
                        }
                      });
                    }
                  }
                } catch (error) {
                  console.error('Error parsing service data:', error);
                  // Fallback to old format
                  const selectedService = dropdownAnswer.answer;
                  allTopics.push({
                    id: `service-${selectedService}`,
                    title: selectedService,
                    subtitle: 'Password Service',
                    data: 'Error parsing service data',
                    onEdit: () => {
                      const route = sectionRoutes['1204A'];
                      console.log('PasswordsReview: Navigating to service (old format)');
                      console.log('PasswordsReview: Route:', route);
                      if (route) navigate(`${route}?fromReview=1`);
                    }
                  });
                }
              }
            }
            
            // Also show any other answers in this section that aren't part of the main service selection
            section.answers.forEach((answer) => {
              if (!['p1', 'p2', 'p3', 'p4', 'p5', 'p6', 'p7', 'p8'].includes(answer.originalQuestionId)) {
                const questionId = answer.originalQuestionId;

                // Skip if we've already processed this question
                if (processedQuestions.has(questionId)) {
                  return;
                }

                const allQuestions = passwordsData['1204'];
                const questionData = allQuestions?.find((q: any) => q.id === questionId);
                if (questionData) {
                  // Mark this question as processed
                  processedQuestions.add(questionId);

                  allTopics.push({
                    id: questionId,
                    title: questionData.text,
                    subtitle: `Section: ${section.originalSectionId}`,
                    data: answer.answer,
                    onEdit: () => {
                      const route = sectionRoutes['1204A'];
                      console.log('PasswordsReview: Navigating to question:', questionId);
                      console.log('PasswordsReview: Route:', route);
                      if (route) {
                        navigate(`${route}?questionId=${questionId}`);
                      }
                    }
                  });
                }
              }
            });
          } else {
            // Default logic for all other sections
            section.answers.forEach((answer) => {
              const questionId = answer.originalQuestionId;

              // Skip if we've already processed this question
              if (processedQuestions.has(questionId)) {
                return;
              }

              const sectionKey = questionToSectionMap[questionId];

              // Find the question data from the appropriate section
              let questionData = null;
              let allQuestions = null;

              // Map section IDs to their data keys
              const sectionDataMap: Record<string, string> = {
                '1204A': '1204', // Other Passwords
              };

              const dataKey = sectionDataMap[section.originalSectionId];
              if (dataKey && dataKey in passwordsData) {
                allQuestions = passwordsData[dataKey as keyof typeof passwordsData];
                questionData = allQuestions?.find((q: any) => q.id === questionId);
              }

              if (questionData) {
                // Mark this question as processed
                processedQuestions.add(questionId);

                let displayAnswer = answer.answer;

                // Format different answer types
                if (answer.type === 'password') {
                  displayAnswer = answer.answer;
                } else if (answer.type === 'choice' && questionData.options) {
                  const option = questionData.options.find((opt: any) => opt.value === answer.answer);
                  displayAnswer = option ? option.label : answer.answer;
                }

                allTopics.push({
                  id: questionId,
                  title: questionData.text,
                  subtitle: `Section: ${section.originalSectionId}`,
                  data: displayAnswer,
                  onEdit: () => {
                    const route = sectionRoutes[sectionKey];
                    console.log('PasswordsReview: Navigating to question (general):', questionId);
                    console.log('PasswordsReview: Route:', route);
                    if (route) {
                      navigate(`${route}?questionId=${questionId}`);
                    }
                  }
                });
              }
            });
          }
        });
      }

      setTopics(allTopics);
    }
  }, [userInputs, isLoading, navigate]);

  if (isLoading) {
    return <div className="flex justify-center items-center h-screen">Loading your answers...</div>;
  }

  if (error) {
    return <div className="flex justify-center items-center h-screen text-red-500">{error}</div>;
  }

  if (!user?.id) {
    return <div className="flex justify-center items-center h-screen text-red-500">You must be logged in to view your answers</div>;
  }

  if (userInputs.length === 0 && !isLoading) {
    return <div className="flex justify-center items-center h-screen">No password answers found. Please complete some questions first.</div>;
  }

  return (
    <div className="flex flex-col">
      {/* Banner Section */}
      <div className="w-full bg-gradient-to-r from-[#1F4168] to-[#2BCFD5] py-7 mb-0">
        <div className="container mx-auto flex items-center justify-between px-6">
          <div>
            <div className="text-3xl font-bold text-white mb-1">Passwords</div>
            <div>
              <button 
                onClick={() => navigate('/dashboard')} 
                className="text-[#2BCFD5] text-base opacity-90 hover:underline flex items-center font-semibold text-md mt-1 mb-1"
              >
                <span className="mr-1">←</span> Back to Home
              </button>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="font-semibold text-white">{userInfo.name}</div>
              <div className="text-sm text-white opacity-80">{userInfo.email}</div>
            </div>
            <Avatar className="rounded-full w-16 h-16 bg-white overflow-hidden border-4 border-white shadow-md">
              <img src={userInfo.avatar} alt={userInfo.name} className="w-full h-full object-cover" />
            </Avatar>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 container mx-auto px-6 py-8 flex gap-8">
        {/* Left */}
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-2xl font-bold">My Category Details</h2>
            <button
              className="flex items-center gap-2 px-4 py-2 border border-[#d6e0ef] rounded-md bg-white text-[#222] hover:bg-[#f7f7f7]"
              onClick={() => window.print()}
            >
              Print
            </button>
          </div>
          {/* Info Box */}
          <div className="bg-[#f5f8ff] border border-[#d6e0ef] rounded-md p-4 mb-6 flex items-start gap-3">
            <div>
              <div className="font-semibold text-[#4b4e7a]">Your Password Information</div>
              <div className="text-[#555] text-sm">Review all your password information below. You can edit any section by clicking the edit button.</div>
            </div>
            <button className="ml-auto text-[#888] hover:text-[#222]">×</button>
          </div>
          {/* Topics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {topics.map(topic => (
              <div key={topic.id} className="bg-white border border-[#e5e7ef] rounded-lg p-4 shadow-sm flex flex-col">
                <div className="flex items-center gap-3 mb-2">
                  <div>
                    <div className="font-normal">{topic.title}</div>
                  </div>
                </div>
                <div className="text-[#555] text-xl font-bold italic mb-4">
                  {(() => {
                    let parsed = topic.data;
                    if (typeof parsed === 'string') {
                      try {
                        parsed = JSON.parse(parsed);
                      } catch {
                        // Not JSON, leave as is
                      }
                    }
                    if (Array.isArray(parsed) && parsed.length > 0 && parsed[0].name && (parsed[0].info || parsed[0].phone)) {
                      return (
                        <ul className="list-disc pl-5">
                          {parsed.map((contact, idx) => (
                            <li key={idx}><strong>{contact.name}</strong>: {contact.info || contact.phone}</li>
                          ))}
                        </ul>
                      );
                    }
                    return typeof topic.data === 'string' ? topic.data : JSON.stringify(topic.data);
                  })()}
                </div>
                <button
                  className="self-end px-4 py-1 border border-[#d6e0ef] rounded-md bg-white text-[#222] hover:bg-[#f7f7f7]"
                  onClick={topic.onEdit}
                >
                  Edit
                </button>
              </div>
            ))}
          </div>
          <div className="flex justify-end mt-8 mr-2">
            <button
              onClick={() => navigate('/category/socialmedia')}
              className="px-8 py-3 bg-[#2BCFD5] text-white rounded-md hover:bg-[#1F4168] transition-colors duration-200 shadow-md font-semibold text-md mt-1 mb-1"
            >
              Continue to Social and Media
            </button>
          </div>
        </div>
        {/* Right */}
        <div className="w-full max-w-sm">
          <SearchPanel />
        </div>
      </div>
    </div>
  );
};

export default PasswordsReview;
