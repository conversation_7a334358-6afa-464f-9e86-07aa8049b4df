import { useField } from 'formik';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import * as Yup from 'yup';

export interface BaseQuestion {
  id: string;
  text: string;
  type: string;
  required: boolean;
  sectionId: string;
  order: number;
  isAnswered?: boolean;
  answer?: any;
  dependsOn?: {
    questionId: string;
    value: string;
  };
  options?: Array<{
    value: string;
    label: string;
  }>;
}

export interface TextQuestion extends BaseQuestion {
  type: 'text' | 'email' | 'password';
}

export interface TextareaQuestion extends BaseQuestion {
  type: 'textarea';
}

export interface BooleanQuestion extends BaseQuestion {
  type: 'boolean' | 'yesno';
}

export interface ChoiceQuestion extends BaseQuestion {
  type: 'choice';
  options: Array<{
    value: string;
    label: string;
  }>;
}

export interface DropdownQuestion extends BaseQuestion {
  type: 'dropdown';
  options: Array<{
    value: string;
    label: string;
  }>;
}

export type Question = TextQuestion | TextareaQuestion | BooleanQuestion | ChoiceQuestion | DropdownQuestion;

// Form field components
const TextField = ({ question }: { question: TextQuestion }) => {
  const [field, meta] = useField(question.id);
  
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
        {question.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <Input
        id={question.id}
        type={question.type}
        {...field}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

const TextareaField = ({ question }: { question: TextareaQuestion }) => {
  const [field, meta] = useField(question.id);
  
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
        {question.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <Textarea
        id={question.id}
        {...field}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
        rows={4}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

const BooleanField = ({ question }: { question: BooleanQuestion }) => {
  const [field, meta, helpers] = useField(question.id);
  
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium">
        {question.text}
        {question.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <div className="flex gap-4">
        <Button
          type="button"
          variant={field.value === 'yes' ? 'default' : 'outline'}
          onClick={() => helpers.setValue('yes')}
          className="flex-1"
        >
          Yes
        </Button>
        <Button
          type="button"
          variant={field.value === 'no' ? 'default' : 'outline'}
          onClick={() => helpers.setValue('no')}
          className="flex-1"
        >
          No
        </Button>
      </div>
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

const ChoiceField = ({ question }: { question: ChoiceQuestion }) => {
  const [field, meta, helpers] = useField(question.id);
  
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium">
        {question.text}
        {question.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
        {question.options?.map((option) => (
          <Button
            key={option.value}
            type="button"
            variant={field.value === option.value ? 'default' : 'outline'}
            onClick={() => helpers.setValue(option.value)}
            className="text-left justify-start"
          >
            {option.label}
          </Button>
        ))}
      </div>
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

const DropdownField = ({ question }: { question: DropdownQuestion }) => {
  const [field, meta, helpers] = useField(question.id);
  
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
        {question.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <Select value={field.value} onValueChange={(value) => helpers.setValue(value)}>
        <SelectTrigger className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}>
          <SelectValue placeholder="Select an option" />
        </SelectTrigger>
        <SelectContent>
          {question.options?.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

// Main QuestionItem component
export const QuestionItem = ({ question, values }: { question: Question; values: Record<string, any> }) => {
  // Check if question should be shown based on dependencies
  if (question.dependsOn) {
    const dependentValue = values[question.dependsOn.questionId];
    if (dependentValue !== question.dependsOn.value) {
      return null;
    }
  }

  switch (question.type) {
    case 'text':
    case 'email':
    case 'password':
      return <TextField question={question as TextQuestion} />;
    case 'textarea':
      return <TextareaField question={question as TextareaQuestion} />;
    case 'boolean':
    case 'yesno':
      return <BooleanField question={question as BooleanQuestion} />;
    case 'choice':
      return <ChoiceField question={question as ChoiceQuestion} />;
    case 'dropdown':
      return <DropdownField question={question as DropdownQuestion} />;
    default:
      return null;
  }
};

// Helper functions
export const buildValidationSchema = (questions: Question[]) => {
  const schema: Record<string, any> = {};

  questions.forEach((question) => {
    let fieldSchema: any = Yup.string();

    if (question.required) {
      fieldSchema = fieldSchema.required(`${question.text} is required`);
    }

    schema[question.id] = fieldSchema;
  });

  return Yup.object().shape(schema);
};

export const generateInitialValues = (questions: Question[], existingValues?: Record<string, any>) => {
  const initialValues: Record<string, any> = {};

  questions.forEach((question) => {
    initialValues[question.id] = existingValues?.[question.id] || '';
  });

  return initialValues;
};

// Handle dependent questions logic
export const handleDependentAnswers = (values: Record<string, any>, questions: Question[]) => {
  const updatedValues = { ...values };

  questions.forEach((question) => {
    if (question.dependsOn) {
      const dependentValue = values[question.dependsOn.questionId];
      if (dependentValue !== question.dependsOn.value) {
        // Clear the value if the dependency is not met
        updatedValues[question.id] = '';
      }
    }
  });

  return updatedValues;
};

// Helper function to map frontend question types to backend-accepted enum values
export const mapQuestionTypeForBackend = (type: string): string => {
  switch (type) {
    case 'yesno':
      return 'boolean';
    case 'dropdown':
      return 'choice';
    case 'password':
    case 'email':
      return 'text';
    default:
      return type;
  }
};
