"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const stripeController_1 = require("../controller/stripeController");
const corsDebug_1 = require("../utils/corsDebug");
const stripe_1 = __importDefault(require("stripe"));
const SubscribedPlan_1 = __importDefault(require("../models/SubscribedPlan"));
const Owner_1 = __importDefault(require("../models/Owner"));
const PricingPlan_1 = __importDefault(require("../models/PricingPlan"));
const User_1 = __importDefault(require("../models/User"));
const mongoose_1 = __importDefault(require("mongoose"));
// import { SubscribeStripe } from "../controller/stripeController";
const stripe = new stripe_1.default(process.env.STRIPE_SECRET_KEY);
const router = express_1.default.Router();
// Add CORS debug middleware for development
if (process.env.NODE_ENV === 'development') {
    router.use(corsDebug_1.corsDebugMiddleware);
}
// Test endpoint to verify CORS
router.get('/test-cors', corsDebug_1.testCorsEndpoint);
// Regular JSON endpoints
router.get('/subscribe', stripeController_1.SubscribeStripe);
router.get('/success', stripeController_1.successPayment);
router.get('/customer/:customerId', stripeController_1.customerDetails);
// Stripe webhook endpoint - uses raw body stored by body-parser for signature verification
router.post('/', express_1.default.raw({ type: 'application/json' }), stripeController_1.stripeWebhooks);
// Manual webhook endpoint for local testing - simulates Stripe payment completion
router.post('/manual-webhook', ((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Only allow in development mode or when NODE_ENV is not set (local development)
        if (process.env.NODE_ENV === 'production') {
            return res.status(403).json({ error: 'This endpoint is not available in production mode' });
        }
        const { email, planType, sessionId } = req.body;
        if (!email || !planType) {
            return res.status(400).json({ error: 'Email and planType are required' });
        }
        console.log('🔧 Manual webhook triggered for:', { email, planType, sessionId });
        // Find the pricing plan
        const plan = yield PricingPlan_1.default.findOne({ type: planType });
        if (!plan) {
            return res.status(400).json({ error: `Plan type '${planType}' not found` });
        }
        // Check if owner already exists
        let owner = yield Owner_1.default.findOne({ email: email.toLowerCase() });
        if (!owner) {
            // Create temporary owner record (simulating webhook behavior)
            console.log('Creating temporary owner for email:', email);
            owner = new Owner_1.default({
                email: email.toLowerCase(),
                externalUser: false,
                username: '',
                firstName: '',
                lastName: '',
                userId: new mongoose_1.default.Types.ObjectId(), // Temporary placeholder
                stripeSessionId: sessionId || `manual_${Date.now()}`
            });
            yield owner.save();
            console.log('Created temporary owner with ID:', owner._id);
        }
        else {
            // Update existing owner with session ID if provided
            if (sessionId) {
                owner.stripeSessionId = sessionId;
                yield owner.save();
            }
        }
        // Check if subscription already exists
        let subscription = yield SubscribedPlan_1.default.findOne({ ownerId: owner._id });
        if (!subscription) {
            console.log('Creating new subscription...');
            subscription = new SubscribedPlan_1.default({
                planId: plan._id,
                ownerId: owner._id,
                currentPlan: plan._id,
                previousPlans: []
            });
            yield subscription.save();
            console.log('New subscription saved:', subscription._id);
            // Update owner with subscription ID
            owner.subscribedPlanId = subscription._id;
            yield owner.save();
            console.log('Owner updated with subscription ID');
        }
        else {
            console.log('Subscription already exists:', subscription._id);
        }
        // Update any existing users with this owner's subscription type
        const users = yield User_1.default.find({ ownerId: owner._id });
        if (users.length > 0) {
            yield User_1.default.updateMany({ ownerId: owner._id }, {
                subscriptionType: plan.type,
                pricingPlan: plan.type, // Add pricingPlan field for frontend compatibility
                allowedCategoryId: null // Reset allowed category for new subscription type
            });
            console.log('Updated', users.length, 'users with subscription type:', plan.type);
        }
        res.json({
            success: true,
            message: 'Manual webhook processed successfully',
            data: {
                ownerId: owner._id,
                subscriptionId: subscription._id,
                planType: plan.type,
                email: email
            }
        });
    }
    catch (error) {
        console.error('Manual webhook error:', error);
        res.status(500).json({ error: 'Failed to process manual webhook' });
    }
})));
// Check subscription status for a user (no auth required for testing)
router.get('/check-subscription/:ownerId', (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { ownerId } = req.params;
        const owner = yield Owner_1.default.findById(ownerId);
        const subscription = yield SubscribedPlan_1.default.findOne({ ownerId });
        res.json({
            owner: {
                _id: owner === null || owner === void 0 ? void 0 : owner._id,
                subscribedPlanId: owner === null || owner === void 0 ? void 0 : owner.subscribedPlanId,
                email: owner === null || owner === void 0 ? void 0 : owner.email
            },
            subscription: subscription ? {
                _id: subscription._id,
                planId: subscription.planId,
                currentPlan: subscription.currentPlan,
                ownerId: subscription.ownerId
            } : null
        });
    }
    catch (error) {
        console.error('Error checking subscription:', error);
        res.status(500).json({ error: 'Failed to check subscription' });
    }
}));
// Manual test endpoint for development - simulate webhook processing
router.post('/test-webhook', ((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    if (process.env.NODE_ENV !== 'development') {
        return res.status(403).json({ error: 'Forbidden' });
    }
    try {
        const { sessionId } = req.body;
        if (!sessionId) {
            return res.status(400).json({ error: 'Session ID required' });
        }
        // Retrieve the session from Stripe
        const session = yield stripe.checkout.sessions.retrieve(sessionId, {
            expand: ['subscription']
        });
        // Simulate webhook processing
        const metadata = session.metadata || {};
        const ownerId = metadata.ownerId;
        const planId = metadata.planId;
        if (ownerId && planId) {
            const existing = yield SubscribedPlan_1.default.findOne({ ownerId });
            if (!existing) {
                const subscription = new SubscribedPlan_1.default({
                    planId,
                    ownerId,
                    currentPlan: planId,
                    previousPlans: []
                });
                yield subscription.save();
                yield Owner_1.default.findByIdAndUpdate(ownerId, { subscribedPlanId: subscription._id });
                res.json({ success: true, message: 'Subscription created manually', subscriptionId: subscription._id });
            }
            else {
                res.json({ success: true, message: 'Subscription already exists', subscriptionId: existing._id });
            }
        }
        else {
            res.status(400).json({ error: 'Missing metadata in session' });
        }
    }
    catch (error) {
        console.error('Manual webhook test error:', error);
        res.status(500).json({ error: 'Failed to process webhook manually' });
    }
})));
exports.default = router;
//# sourceMappingURL=stripeRoutes.js.map