import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import GradiantHeader from "@/mobile/components/header/gradiantHeader";
import CategoryReviewPage from '@/mobile/components/category/CategoryReviewPage';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  selectUserInputs,
  selectLoading,
  selectError,
  UserInput
} from '@/store/slices/passwordsSlice';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { Button } from '@/components/ui/button';
import passwordsData from '@/data/Passwords.json';

// Map section IDs to their routes
const sectionRoutes: Record<string, string> = {
  '1204A': 'otherpasswords',
};

interface Topic {
  id: string;
  title: string;
  subtitle?: string;
  data: string;
  onEdit: () => void;
}

export default function PasswordsReviewPage() {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [topics, setTopics] = useState<Topic[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Get data from Redux store
  const userInputs = useAppSelector(selectUserInputs);
  const isLoading = useAppSelector(selectLoading);
  const reduxError = useAppSelector(selectError);

  useEffect(() => {
    const fetchUserAnswers = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      }
    };

    fetchUserAnswers();
  }, [dispatch, user]);

  useEffect(() => {
    if (!isLoading && userInputs.length > 0) {
      const allTopics: Topic[] = [];

      // Filter userInputs to only include passwords category (originalCategoryId: '12')
      // and get the most recent one to avoid duplicates
      const passwordUserInputs = userInputs.filter((input: UserInput) =>
        input.originalCategoryId === '12'
      );

      // Sort by creation date and take the most recent one
      // Use _id as fallback since MongoDB ObjectIds contain timestamp information
      const sortedInputs = passwordUserInputs.sort((a, b) => {
        // Try to use createdAt if available (from backend timestamps)
        const aTime = (a as any).createdAt ? new Date((a as any).createdAt).getTime() : 0;
        const bTime = (b as any).createdAt ? new Date((b as any).createdAt).getTime() : 0;

        // If both have createdAt, use that
        if (aTime && bTime) {
          return bTime - aTime; // Most recent first
        }

        // Fallback to _id comparison (MongoDB ObjectIds contain timestamp)
        if (a._id && b._id) {
          return a._id > b._id ? -1 : 1; // Most recent first
        }

        // Final fallback - keep original order
        return 0;
      });

      // Only process the most recent password input to avoid duplicates
      const latestPasswordInput = sortedInputs[0];

      if (latestPasswordInput) {
        const userInput = latestPasswordInput;
        userInput.answersBySection.forEach((section) => {
          // First, handle yes/no questions to show them first
          section.answers.forEach((answer) => {
            // Find the original question from our data
            const questionId = answer.originalQuestionId;
            const allQuestions = passwordsData['1204'];
            const questionData = allQuestions?.find((q: any) => q.id === questionId);

            if (questionData && questionData.type === 'yesno') {
              let displayAnswer = answer.answer;
              
              // Format yes/no answers for better display
              if (answer.answer === 'yes') {
                displayAnswer = 'Yes';
              } else if (answer.answer === 'no') {
                displayAnswer = 'No';
              }

              allTopics.push({
                id: questionId,
                title: questionData.text,
                subtitle: 'Password Preference',
                data: displayAnswer,
                onEdit: () => {
                  const route = sectionRoutes['1204A'];
                  if (route) {
                    navigate(`/category/passwords/${route}?questionId=${questionId}&fromReview=1`);
                  }
                }
              });
            }
          });

          // Then handle service data (only if they answered yes)
          const yesNoAnswer = section.answers.find(a => a.originalQuestionId === 'p2');
          if (yesNoAnswer?.answer === 'yes') {
            // Special handling for Other Passwords (section 1204A)
            if (section.originalSectionId === '1204A') {
              // Find the main dropdown selection
              const dropdownAnswer = section.answers.find(a => a.originalQuestionId === 'p1');
              
              if (dropdownAnswer?.answer) {
                try {
                  // Parse the JSON structure for services
                  const servicesData = JSON.parse(dropdownAnswer.answer);
                  
                  if (Array.isArray(servicesData)) {
                    // Handle new structure with multiple services and accounts
                    servicesData.forEach((service: any, index: number) => {
                      if (service.serviceName && service.accounts && Array.isArray(service.accounts)) {
                        const accounts = service.accounts.filter((account: any) =>
                          account.username || account.password
                        );
                        
                        if (accounts.length > 0) {
                          // Format accounts for display
                          const displayName = service.displayName || service.serviceName;
                          let accountsDisplay = `Service: ${displayName}\n`;
                          accounts.forEach((account: any, accountIndex: number) => {
                            accountsDisplay += `Account ${accountIndex + 1}\n`;
                            const parts = [];
                            if (account.username) {
                              parts.push(`Username: ${account.username}`);
                            }
                            if (account.password) {
                              parts.push(`Password: ${account.password}`);
                            }
                            accountsDisplay += parts.join('  |  ') + '\n';
                          });
                          
                          allTopics.push({
                            id: `service-${displayName}-${index}`,
                            title: displayName,
                            subtitle: `${accounts.length} account${accounts.length !== 1 ? 's' : ''}`,
                            data: accountsDisplay.trim(),
                            onEdit: () => {
                              const route = sectionRoutes['1204A'];
                              if (route) {
                                // Pass the service index for highlighting and fromReview flag
                                navigate(`/category/passwords/${route}?serviceIndex=${index}&fromReview=1`);
                              }
                            }
                          });
                        } else {
                          // Service with no valid accounts
                          const displayName = service.displayName || service.serviceName;
                          allTopics.push({
                            id: `service-${displayName}-${index}`,
                            title: displayName,
                            subtitle: 'Password Service',
                            data: 'No credentials provided',
                            onEdit: () => {
                              const route = sectionRoutes['1204A'];
                              if (route) {
                                navigate(`/category/passwords/${route}?serviceIndex=${index}&fromReview=1`);
                              }
                            }
                          });
                        }
                      }
                    });
                  } else {
                    // Handle old single service format
                    const selectedService = dropdownAnswer.answer;
                    
                    if (selectedService === 'Other') {
                      // Handle "Other" service with custom name
                      const serviceNameAnswer = section.answers.find(a => a.originalQuestionId === 'p2');
                      const serviceName = serviceNameAnswer?.answer || 'Custom Service';
                      
                      allTopics.push({
                        id: 'other-service-custom',
                        title: serviceName,
                        subtitle: 'Custom Service',
                        data: 'Service added manually',
                        onEdit: () => {
                          const route = sectionRoutes['1204A'];
                          if (route) {
                            navigate(`/category/passwords/${route}`);
                          }
                        }
                      });
                    } else {
                      // Handle specific service selection (old format)
                      const usernameAnswer = section.answers.find(a => 
                        a.originalQuestionId === 'p3' || // Username field
                        a.originalQuestionId === 'p5' || // Additional username fields
                        a.originalQuestionId === 'p7'    // More username fields
                      );
                      
                      const passwordAnswer = section.answers.find(a => 
                        a.originalQuestionId === 'p4' || // Password field
                        a.originalQuestionId === 'p6' || // Additional password fields
                        a.originalQuestionId === 'p8'    // More password fields
                      );
                      
                      const credentials = [];
                      if (usernameAnswer?.answer) {
                        credentials.push(`Username: ${usernameAnswer.answer}`);
                      }
                      if (passwordAnswer?.answer) {
                        credentials.push(`Password: ${passwordAnswer.answer}`);
                      }
                      
                      allTopics.push({
                        id: `service-${selectedService}`,
                        title: selectedService,
                        subtitle: 'Password Service',
                        data: credentials.length > 0 ? credentials.join('  |  ') : 'No credentials provided',
                        onEdit: () => {
                          const route = sectionRoutes['1204A'];
                          if (route) {
                            navigate(`/category/passwords/${route}`);
                          }
                        }
                      });
                    }
                  }
                } catch (error) {
                  console.error('Error parsing service data:', error);
                }
              }
            }
          }
        });
      }

      setTopics(allTopics);
    }
  }, [isLoading, userInputs, navigate]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <GradiantHeader title="Passwords Review" showAvatar={true} />
        <div className="container mx-auto px-4 py-6 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2BCFD5] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your password information...</p>
        </div>
      </div>
    );
  }

  if (error || reduxError) {
    return (
      <div className="min-h-screen bg-gray-50">
        <GradiantHeader title="Passwords Review" showAvatar={true} />
        <div className="container mx-auto px-4 py-6">
          <Alert variant="destructive">
            <AlertDescription>{error || reduxError}</AlertDescription>
          </Alert>
          <Button
            onClick={() => navigate('/category/passwords')}
            className="mt-4 w-full bg-[#2BCFD5] hover:bg-[#25b5ba] text-white"
          >
            Back to Passwords
          </Button>
        </div>
      </div>
    );
  }

  return (
    <CategoryReviewPage
      categoryTitle="Passwords"
      infoTitle="How to edit your information"
      infoDescription="Review the details about your password services. Tap Edit on any item to update it."
      topics={topics}
      onPrint={() => window.print()}
      afterTopics={
        <Button 
          onClick={() => navigate('/category/subscription')}
          className="px-8 py-3 bg-[#2BCFD5] text-white rounded-md hover:bg-[#1F4168] transition-colors duration-200 shadow-md font-semibold text-md mt-1 mb-1"
        >
          Continue to subscription
        </Button>
      }
    />
  );
} 