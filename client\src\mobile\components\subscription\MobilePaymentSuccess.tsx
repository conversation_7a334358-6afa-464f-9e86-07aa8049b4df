import React, { useEffect, useState, useRef } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, AlertCircle, ArrowRight } from 'lucide-react';
import { HeirKeyCardLoader } from '@/components/ui/heirkey-loader';
import { useToast } from '@/hooks/use-toast';
import stripeService from '@/services/stripeService';
import subscriptionService from '@/services/subscriptionService';
import authService from '@/services/authService';
import { useAuth } from '@/contexts/AuthContext';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';

export default function MobilePaymentSuccess() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, setUser } = useAuth();
  const { plans } = useSelector((state: RootState) => state.subscription);
  
  const [isLoading, setIsLoading] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [sessionData, setSessionData] = useState<any>(null);

  const sessionId = searchParams.get('session_id');
  const hasVerified = useRef(false);

  // Function to refresh user data after subscription creation
  const refreshUserData = async () => {
    try {
      console.log('Mobile Payment Success: Refreshing user data after subscription');
      const updatedUser = await authService.getProfile();
      setUser(updatedUser);
      console.log('Mobile Payment Success: User data refreshed successfully');
    } catch (error) {
      console.error('Mobile Payment Success: Error refreshing user data:', error);
      // Don't fail the success page for user refresh errors
    }
  };

  useEffect(() => {
    if (!sessionId || hasVerified.current) return;
    hasVerified.current = true;

    const verifyPayment = async () => {
      if (!sessionId) {
        setError('No session ID found');
        setIsLoading(false);
        return;
      }

      try {
        // Handle free plan sessions
        if (sessionId === 'free_plan') {
          const planType = searchParams.get('plan') || 'temporary_key';
          setSessionData({
            id: 'free_plan_session',
            payment_status: 'paid',
            metadata: { planType }
          });
          setIsSuccess(true);
          
          // Create subscription for free plan
          if (user?.id) {
            try {
              console.log('Mobile Payment Success: Creating free plan subscription for user:', user.id);
              const ownerId = await getCachedOwnerIdFromUser(user);
              if (ownerId) {
                console.log('Mobile Payment Success: Found owner ID:', ownerId);
                const plan = plans.find(p => p.type === planType);
                const planId = plan?._id;
                if (planId) {
                  console.log('Mobile Payment Success: Creating subscription with plan ID:', planId);
                  await subscriptionService.subscribeToPlan(planId, ownerId);
                  console.log('Mobile Payment Success: Free plan subscription created successfully');
                  
                  // Refresh user data to update subscription type in frontend
                  await refreshUserData();
                  
                  toast({
                    title: "Free Plan Activated",
                    description: "Your free plan has been successfully activated!",
                  });
                } else {
                  console.error('Mobile Payment Success: Plan not found for type:', planType);
                  toast({
                    title: "Subscription Error",
                    description: "Could not find the selected plan. Please contact support.",
                    variant: "destructive",
                  });
                }
              } else {
                console.error('Mobile Payment Success: Could not get owner ID for user');
              }
            } catch (subscriptionError) {
              console.error('Mobile Payment Success: Error creating free plan subscription:', subscriptionError);
              toast({
                title: "Subscription Error",
                description: "Failed to activate free plan. Please contact support.",
                variant: "destructive",
              });
            }
          }
          setIsLoading(false);
          return;
        }

        // Verify the payment with Stripe for paid plans
        console.log('Mobile Payment Success: Verifying Stripe payment for session:', sessionId);
        const session = await stripeService.getCheckoutSession(sessionId);
        setSessionData(session);

        if (session.payment_status === 'paid') {
          setIsSuccess(true);
          
          // If user is logged in, try to create subscription in our database
          if (user?.id) {
            try {
              console.log('Mobile Payment Success: Creating paid subscription for user:', user.id);
              const ownerId = await getCachedOwnerIdFromUser(user);
              if (ownerId) {
                console.log('Mobile Payment Success: Found owner ID:', ownerId);
                // Get the plan type from the session or URL params
                const planType = searchParams.get('plan') || 'spare_key'; // default fallback
                console.log('Mobile Payment Success: Plan type from URL:', planType);
                // Find the plan by type to get the ObjectId
                const plan = plans.find(p => p.type === planType);
                const planId = plan?._id;
                if (planId) {
                  console.log('Mobile Payment Success: Creating subscription with plan ID:', planId);
                  // Create subscription in our database with the correct ObjectId
                  // This will automatically update the user database with subscriptionType
                  await subscriptionService.subscribeToPlan(planId, ownerId);
                  console.log('Mobile Payment Success: Paid subscription created successfully');
                  
                  // Refresh user data to update subscription type in frontend
                  await refreshUserData();
                  
                  toast({
                    title: "Subscription Created",
                    description: "Your subscription has been successfully activated!",
                  });
                } else {
                  console.error('Mobile Payment Success: Plan not found for type:', planType);
                  toast({
                    title: "Subscription Error",
                    description: "Could not find the selected plan. Please contact support.",
                    variant: "destructive",
                  });
                }
              } else {
                console.error('Mobile Payment Success: Could not get owner ID for user');
                toast({
                  title: "Subscription Error",
                  description: "Could not identify your account. Please contact support.",
                  variant: "destructive",
                });
              }
            } catch (subscriptionError) {
              console.error('Mobile Payment Success: Error creating subscription:', subscriptionError);
              // Don't fail the success page for subscription creation errors
              toast({
                title: "Payment Successful",
                description: "Payment verified but subscription creation failed. Please contact support.",
                variant: "destructive",
              });
            }
          } else {
            console.log('Mobile Payment Success: No user logged in, skipping subscription creation');
          }
        } else {
          console.error('Mobile Payment Success: Payment status not paid:', session.payment_status);
          setError('Payment was not successful');
        }
      } catch (error) {
        console.error('Mobile Payment Success: Error verifying payment:', error);
        setError('Failed to verify payment');
      } finally {
        setIsLoading(false);
      }
    };

    verifyPayment();
  }, [sessionId, user, plans, searchParams, toast, setUser]);

  const handleContinue = () => {
    navigate('/dashboard');
  };

  const handleContactSupport = () => {
    // You can implement this to open a support chat or email
    window.open('mailto:<EMAIL>', '_blank');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center p-4">
        <Card className="w-full max-w-sm mx-auto border-0 shadow-none">
          <CardContent>
            <HeirKeyCardLoader text="Verifying Payment" />
            <p className="text-gray-600 text-center text-sm mt-2">
              Please wait while we verify your payment...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white flex items-center justify-center p-4">
      <Card className="w-full max-w-sm mx-auto border-0 shadow-none">
        <CardHeader className="text-center pb-6">
          {isSuccess ? (
            <div className="flex flex-col items-center">
              <CheckCircle className="h-20 w-20 text-green-500 mb-6" />
              <CardTitle className="text-2xl font-bold text-green-700">
                Payment Successful!
              </CardTitle>
            </div>
          ) : (
            <div className="flex flex-col items-center">
              <AlertCircle className="h-20 w-20 text-red-500 mb-6" />
              <CardTitle className="text-2xl font-bold text-red-700">
                Payment Verification Failed
              </CardTitle>
            </div>
          )}
        </CardHeader>
        
        <CardContent className="space-y-6">
          {isSuccess ? (
            <>
              <div className="text-center space-y-3">
                <p className="text-gray-600 text-sm">
                  Thank you for your subscription! Your payment has been processed successfully.
                </p>
                {sessionData && (
                  <div className="bg-gray-50 rounded-lg p-4 text-sm">
                    <p className="font-bold mb-2 text-[#1F4168]">Transaction ID:</p>
                    <div className="select-all break-all text-gray-800 mb-2 font-mono text-xs">
                      {sessionData.id}
                    </div>
                    <p className="text-gray-600">Status: {sessionData.payment_status}</p>
                  </div>
                )}
              </div>
              
              <div className="space-y-3">
                <Button 
                  onClick={handleContinue}
                  className="w-full bg-[#2BCFD5] hover:bg-[#1F4168] py-3 text-base font-semibold"
                >
                  Continue to Dashboard
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                
                <p className="text-xs text-gray-500 text-center">
                  You will receive a confirmation email shortly.
                </p>
              </div>
            </>
          ) : (
            <>
              <div className="text-center space-y-3">
                <p className="text-gray-600 text-sm">
                  {error || 'There was an issue verifying your payment. Please contact our support team.'}
                </p>
              </div>
              
              <div className="space-y-3">
                <Button 
                  onClick={handleContactSupport}
                  variant="outline"
                  className="w-full py-3 text-base"
                >
                  Contact Support
                </Button>
                
                <Button 
                  onClick={() => navigate('/subscription')}
                  className="w-full bg-[#2BCFD5] hover:bg-[#1F4168] py-3 text-base font-semibold"
                >
                  Try Again
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 