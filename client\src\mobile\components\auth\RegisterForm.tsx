import { Link, useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import google from '@/assets/mobileimage/global/google.svg';
import AuthHeader from '../header/gradiantHeader';
import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, Crown } from 'lucide-react';
import { HeirKeyInlineLoader } from '@/components/ui/heirkey-loader';
import logo from '@/assets/global/logo.png';
import SessionStorageManager, { PurchasedPlanData } from '@/utils/sessionStorage';

export default function RegisterForm() {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [purchasedPlan, setPurchasedPlan] = useState<PurchasedPlanData | null>(null);

  const { register, isLoading } = useAuth();
  const navigate = useNavigate();

  const hasValidPassword =
    password.length >= 8 && /[!@#$%^&*(),.?":{}|<>]/.test(password);
  const passwordsMatch = password === confirmPassword && password !== '';

  // Check for purchased plan on component mount
  useEffect(() => {
    const planData = SessionStorageManager.getPurchasedPlan();
    if (planData) {
      setPurchasedPlan(planData);
      console.log('Found purchased plan data:', planData);
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!hasValidPassword || !passwordsMatch) {
      setError("Please ensure your password meets all requirements.");
      return;
    }

    try {
      // Include purchased plan data in registration request
      const registrationData: {
        username: string;
        email: string;
        password: string;
        firstName?: string;
        lastName?: string;
        sessionId?: string;
        planType?: string;
      } = {
        username,
        email,
        password,
        firstName: firstName || undefined,
        lastName: lastName || undefined
      };

      // Add session data if available
      if (purchasedPlan) {
        registrationData.sessionId = purchasedPlan.sessionId;
        registrationData.planType = purchasedPlan.planType;
      }

      const response = await register(registrationData);

      // Don't clear purchased plan data yet - wait until after email verification
      // SessionStorageManager.clearPurchasedPlan();

      // Check if email verification is required
      if (response?.requiresVerification) {
        // Redirect to web verification form with email
        navigate("/auth/verify", {
          state: {
            email: response.email || email,
            message: response.message
          }
        });
      } else {
        // Clear purchased plan data only if no verification is required
        SessionStorageManager.clearPurchasedPlan();
        // Legacy flow: redirect to dashboard
        navigate("/dashboard");
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Registration failed. Please try again.";
      setError(errorMessage);
    }
  };

  const handleGoogleSignup = () => {
    // Redirect to Google OAuth signup endpoint
    window.location.href = `${import.meta.env.VITE_API_URL}/v1/auth/google/signup`;
  };

  return (
    <>
      <AuthHeader title="Sign up or Login" logo={logo} />

      {/* Tab Navigation */}
      <div className="flex mb-6 border-b w-full max-w-md md:max-w-xl lg:max-w-2xl mx-auto mt-10">
        <button className="flex-1 py-2 text-center font-medium text-[#2BCFD5] border-b-2 border-[#2BCFD5]">
          Sign up
        </button>
        <Link
          to="/auth/login"
          className="flex-1 py-2 text-center text-gray-500 hover:text-[#2BCFD5] transition"
        >
          Log in
        </Link>
      </div>

      {/* Card Container */}
      <Card className="w-full max-w-sm md:max-w-xl lg:max-w-2xl mx-auto p-4 md:p-6 shadow-md">
        <CardContent className='mt-7'>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Display purchased plan information */}
          {purchasedPlan && (
            <Card className="border-green-200 bg-green-50 mb-4">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-green-800 text-base">
                  <CheckCircle className="h-4 w-4" />
                  Plan Selected Successfully
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Crown className="h-6 w-6 text-yellow-600" />
                    <div>
                      <h3 className="font-semibold text-gray-900 text-sm">
                        {purchasedPlan.planName || purchasedPlan.planType}
                      </h3>
                      <p className="text-xs text-gray-600">
                        {purchasedPlan.planPrice === 0 ? 'Free Plan' :
                         purchasedPlan.planPrice !== undefined ? `$${purchasedPlan.planPrice}/month` :
                         purchasedPlan.planType === 'all_access_key' ? '$12/month' :
                         purchasedPlan.planType === 'spare_key' ? '$10/month' :
                         'Free Plan'}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-xs font-medium text-green-700">Payment Status</p>
                    <p className="text-xs text-green-600 capitalize">{purchasedPlan.paymentStatus}</p>
                  </div>
                </div>
                <p className="text-xs text-gray-600 mt-2">
                  Complete your registration below to activate your subscription.
                </p>
              </CardContent>
            </Card>
          )}

          <form className="space-y-5" onSubmit={handleSubmit}>
            <Input
              placeholder="Enter your username"
              className="w-full p-3 text-base"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
            />
            <Input
              type="email"
              placeholder="Enter your email"
              className="w-full p-3 text-base"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />

            <div className="flex gap-2">
              <Input
                placeholder="First name"
                className="w-1/2 p-3 text-base"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
              />
              <Input
                placeholder="Last name"
                className="w-1/2 p-3 text-base"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
              />
            </div>

            <Input
              type="password"
              placeholder="Enter your password"
              className="w-full p-3 text-base"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
            <Input
              type="password"
              placeholder="Confirm your password"
              className="w-full p-3 text-base"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
            />

            {/* Password hints */}
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2 text-gray-500">
                <div
                  className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                    hasValidPassword
                      ? 'border-green-500 bg-green-500'
                      : 'border-gray-300'
                  }`}
                >
                  {hasValidPassword && (
                    <div className="w-2 h-2 rounded-full bg-white" />
                  )}
                </div>
                <span className={hasValidPassword ? 'text-green-500' : ''}>
                  Must be at least 8 characters and one special character
                </span>
              </div>
              <div className="flex items-center gap-2 text-gray-500">
                <div
                  className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                    passwordsMatch
                      ? 'border-green-500 bg-green-500'
                      : 'border-gray-300'
                  }`}
                >
                  {passwordsMatch && (
                    <div className="w-2 h-2 rounded-full bg-white" />
                  )}
                </div>
                <span className={passwordsMatch ? 'text-green-500' : ''}>
                  Passwords must match
                </span>
              </div>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full bg-[#2BCFD5] text-white py-3 text-base"
              disabled={!hasValidPassword || !passwordsMatch || isLoading}
            >
              {isLoading ? (
                <>
                  <HeirKeyInlineLoader size="sm" className="mr-2" /> Signing up...
                </>
              ) : (
                "Sign up"
              )}
            </Button>

            {/* Google Signup */}
            <button
              type="button"
              className="w-full mt-4 py-3 px-4 border border-gray-300 rounded-md flex items-center justify-center gap-2 hover:bg-gray-50 text-base"
              onClick={handleGoogleSignup}
            >
              <img src={google} alt="Google" className="w-5 h-5" />
              Sign up with Google
            </button>

            {/* Footer Link */}
            <p className="text-center text-sm text-gray-500 mt-4">
              Already have an account?{' '}
              <Link
                to="/auth/login"
                className="text-[#2BCFD5] hover:text-[#2BCFD5]"
              >
                Log in
              </Link>
            </p>
          </form>
        </CardContent>
      </Card>
    </>
  );
}
