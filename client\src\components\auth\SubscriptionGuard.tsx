import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import SessionStorageManager from '@/utils/sessionStorage';

interface SubscriptionGuardProps {
  children: React.ReactNode;
  redirectTo?: string;
}

/**
 * SubscriptionGuard ensures users have a valid subscription before accessing protected routes.
 * In the new flow:
 * 1. If user is not authenticated, redirect to subscription page
 * 2. If user is authenticated but has no subscription, redirect to subscription page
 * 3. If user has purchased a plan but not registered, allow access to registration
 * 4. If user is authenticated and has subscription, allow access
 */
const SubscriptionGuard: React.FC<SubscriptionGuardProps> = ({
  children,
  redirectTo = '/auth/subscribe'
}) => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const location = useLocation();

  // Show loading state while checking authentication
  if (isLoading) {
    console.log('SubscriptionGuard: Still loading authentication state');
    return <div>Loading...</div>;
  }

  // Check if user has purchased a plan in session (for registration flow)
  const hasPurchasedPlan = SessionStorageManager.hasPurchasedPlan();
  
  // Allow access to registration if user has purchased a plan
  if (hasPurchasedPlan && location.pathname === '/auth/register') {
    return <>{children}</>;
  }

  // If not authenticated, check if this is a logout scenario
  if (!isAuthenticated) {
    // Check if we're coming from a logout (no token and no user data in localStorage)
    const hasStoredToken = localStorage.getItem('token');
    const hasStoredUser = localStorage.getItem('user');

    // If there's no stored auth data, this is likely a fresh visit or logout
    // Redirect to login instead of subscription page
    if (!hasStoredToken && !hasStoredUser) {
      console.log('SubscriptionGuard: User is not authenticated (fresh/logout), redirecting to login');
      return <Navigate to="/auth/login" state={{ from: location }} replace />;
    }

    // Otherwise, redirect to subscription page (new user flow)
    console.log('SubscriptionGuard: User is not authenticated, redirecting to subscription');
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // If authenticated but no pricing plan, redirect to subscription page
  if (isAuthenticated && !user?.pricingPlan) {
    console.log('SubscriptionGuard: User has no pricing plan, redirecting to subscription');
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // User is authenticated and has a subscription, allow access
  return <>{children}</>;
};

export default SubscriptionGuard;
