import React, { useEffect, useState, useRef } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Loader2, AlertCircle, ArrowRight, UserPlus } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import stripeService from '@/services/stripeService';
import subscriptionService from '@/services/subscriptionService';
import { useAuth } from '@/contexts/AuthContext';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '@/store';
import { fetchPricingPlans } from '@/store/slices/subscriptionSlice';
import SessionStorageManager from '@/utils/sessionStorage';

export default function PaymentSuccess() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  const dispatch = useDispatch<AppDispatch>();
  const { plans } = useSelector((state: RootState) => state.subscription);

  const [isLoading, setIsLoading] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [sessionData, setSessionData] = useState<any>(null);

  const sessionId = searchParams.get('session_id');
  const hasVerified = useRef(false);

  // Ensure plans are loaded
  useEffect(() => {
    if (plans.length === 0) {
      dispatch(fetchPricingPlans());
    }
  }, [dispatch, plans.length]);

  useEffect(() => {
    if (!sessionId || hasVerified.current || plans.length === 0) return;
    hasVerified.current = true;

    const verifyPayment = async () => {
      if (!sessionId) {
        setError('No session ID found');
        setIsLoading(false);
        return;
      }

      console.log('PaymentSuccess: Available plans:', plans);

      try {
        // Handle free plan sessions
        if (sessionId === 'free_plan') {
          const planType = searchParams.get('plan') || 'temporary_key';
          const plan = plans.find(p => p.type === planType);

          console.log('PaymentSuccess: Looking for plan type:', planType);
          console.log('PaymentSuccess: Found plan:', plan);

          setSessionData({
            id: 'free_plan_session',
            payment_status: 'paid',
            metadata: { planType }
          });
          setIsSuccess(true);

          // Store plan data in session for registration
          const planPrice = plan?.price !== undefined ? plan.price :
                           planType === 'all_access_key' ? 12 :
                           planType === 'spare_key' ? 10 : 0;

          SessionStorageManager.storePurchasedPlan({
            sessionId: 'free_plan',
            planType: planType,
            planId: plan?._id,
            planName: plan?.type.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()),
            planPrice: planPrice,
            paymentStatus: 'paid'
          });

          console.log('PaymentSuccess: Stored plan data with price:', planPrice);

          toast({
            title: "Plan Selected Successfully",
            description: "Please complete your registration to activate your plan!",
          });

          setIsLoading(false);
          return;
        }

        // Verify the payment with Stripe for paid plans
        const session = await stripeService.getCheckoutSession(sessionId);
        setSessionData(session);

        if (session.payment_status === 'paid') {
          setIsSuccess(true);

          // Get plan information from URL params
          const planType = searchParams.get('plan') || '';
          const plan = plans.find(p => p.type === planType);

          console.log('PaymentSuccess: Paid plan - Looking for plan type:', planType);
          console.log('PaymentSuccess: Paid plan - Found plan:', plan);

          // Store plan data in session for registration
          const planPrice = plan?.price !== undefined ? plan.price :
                           planType === 'all_access_key' ? 12 :
                           planType === 'spare_key' ? 10 : 0;

          SessionStorageManager.storePurchasedPlan({
            sessionId: sessionId,
            planType: planType,
            planId: plan?._id,
            planName: plan?.type.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()),
            planPrice: planPrice,
            paymentStatus: 'paid'
          });

          console.log('PaymentSuccess: Paid plan - Stored plan data with price:', planPrice);

          toast({
            title: "Payment Successful",
            description: "Please complete your registration to activate your subscription!",
          });
        } else {
          setError('Payment was not successful');
        }
      } catch (error) {
        console.error('Error verifying payment:', error);
        setError('Failed to verify payment');
      } finally {
        setIsLoading(false);
      }
    };

    verifyPayment();
  }, [sessionId, user, plans, searchParams, toast]);

  const handleContinue = () => {
    // Check if user is already authenticated
    if (user && isSuccess) {
      // User is already logged in, clear session data and go to dashboard
      SessionStorageManager.clearPurchasedPlan();
      navigate('/dashboard');
    } else {
      // User needs to register, redirect to registration
      navigate('/auth/register');
    }
  };

  const handleContactSupport = () => {
    // You can implement this to open a support chat or email
    window.open('mailto:<EMAIL>', '_blank');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md mx-auto">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <h2 className="text-xl font-semibold mb-2">Verifying Payment</h2>
            <p className="text-gray-600 text-center">
              Please wait while we verify your payment...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          {isSuccess ? (
            <div className="flex flex-col items-center">
              <CheckCircle className="h-16 w-16 text-green-500 mb-4" />
              <CardTitle className="text-2xl font-bold text-green-700">
                Payment Successful!
              </CardTitle>
            </div>
          ) : (
            <div className="flex flex-col items-center">
              <AlertCircle className="h-16 w-16 text-red-500 mb-4" />
              <CardTitle className="text-2xl font-bold text-red-700">
                Payment Verification Failed
              </CardTitle>
            </div>
          )}
        </CardHeader>
        
        <CardContent className="space-y-6">
          {isSuccess ? (
            <>
              <div className="text-center space-y-2">
                <p className="text-gray-600">
                  {user ?
                    "Thank you for your subscription! Your payment has been processed successfully." :
                    "Thank you for your payment! Please complete your registration to activate your subscription."
                  }
                </p>
                {sessionData && (
                  <div className="bg-gray-50 rounded-lg p-3 text-sm">
                    <p className="font-bold mb-1">Transaction ID:</p>
                    <div className="select-all break-all text-gray-800 mb-1">{sessionData.id}</div>
                    <p className="text-gray-600">Status: {sessionData.payment_status}</p>
                  </div>
                )}
              </div>
              
              <div className="space-y-3">
                <Button
                  onClick={handleContinue}
                  className="w-full bg-primary hover:bg-primary/90"
                >
                  {user ? (
                    <>
                      Continue to Dashboard
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </>
                  ) : (
                    <>
                      Complete Registration
                      <UserPlus className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
                
                <p className="text-xs text-gray-500 text-center">
                  {user ?
                    "You will receive a confirmation email shortly." :
                    "After registration, you will receive a confirmation email."
                  }
                </p>
              </div>
            </>
          ) : (
            <>
              <div className="text-center space-y-2">
                <p className="text-gray-600">
                  {error || 'There was an issue verifying your payment. Please contact our support team.'}
                </p>
              </div>
              
              <div className="space-y-3">
                <Button 
                  onClick={handleContactSupport}
                  variant="outline"
                  className="w-full"
                >
                  Contact Support
                </Button>
                
                <Button 
                  onClick={() => navigate('/subscription')}
                  className="w-full"
                >
                  Try Again
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 