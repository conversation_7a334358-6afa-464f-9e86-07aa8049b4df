"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.profileUpdateValidation = exports.loginValidation = exports.registerValidation = void 0;
const joi_1 = __importDefault(require("joi"));
const registerSchema = joi_1.default.object({
    username: joi_1.default.string().required().min(3),
    email: joi_1.default.string().required().email(),
    password: joi_1.default.string()
        .required()
        .min(8)
        .pattern(/[^A-Za-z0-9]/)
        .messages({
        'string.min': 'Password must be at least 8 characters',
        'string.pattern.base': 'Password must contain at least one special character'
    }),
    firstName: joi_1.default.string().optional(),
    lastName: joi_1.default.string().optional(),
    // New fields for subscription-first flow
    sessionId: joi_1.default.string().optional(),
    planType: joi_1.default.string().optional()
});
const loginSchema = joi_1.default.object({
    email: joi_1.default.string().required().email(),
    password: joi_1.default.string().required()
});
const profileUpdateSchema = joi_1.default.object({
    firstName: joi_1.default.string().optional(),
    lastName: joi_1.default.string().optional(),
    username: joi_1.default.string().optional(),
    phone: joi_1.default.string().optional(),
    address: joi_1.default.string().optional(),
    zipCode: joi_1.default.string().optional(),
    country: joi_1.default.string().optional()
});
const registerValidation = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield registerSchema.validateAsync(req.body);
        next();
    }
    catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ message: error.message });
            return;
        }
        res.status(400).json({ message: 'Invalid input' });
    }
});
exports.registerValidation = registerValidation;
const loginValidation = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield loginSchema.validateAsync(req.body);
        next();
    }
    catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ message: error.message });
            return;
        }
        res.status(400).json({ message: 'Invalid input' });
    }
});
exports.loginValidation = loginValidation;
const profileUpdateValidation = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield profileUpdateSchema.validateAsync(req.body);
        next();
    }
    catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ message: error.message });
            return;
        }
        res.status(400).json({ message: 'Invalid input' });
    }
});
exports.profileUpdateValidation = profileUpdateValidation;
//# sourceMappingURL=userValidation.js.map