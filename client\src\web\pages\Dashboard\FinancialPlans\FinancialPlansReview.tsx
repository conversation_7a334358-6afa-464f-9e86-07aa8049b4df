import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import CategoryReviewPage from '@/web/components/Category/CategoryReviewPage';
import { useAuth } from '@/contexts/AuthContext';
import financialPlansData from '@/data/financialPlans.json';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  UserInput as ReduxUserInput
} from '@/store/slices/financialPlansSlice';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { createUserInfo } from '@/utils/avatarUtils';
import { useAccessControl } from '@/hooks/useAccessControl';
import { CATEGORIES } from '@/constants/categories';

// Define interfaces for the data structure
interface Answer {
  index: number;
  questionId?: string;
  originalQuestionId: string;
  question: string;
  type: string;
  answer: string;
}

// Map section IDs to their titles
const sectionTitles: Record<string, string> = {
  '501A': 'Financial Planner',
  '501B': 'Investments',
  '501C': 'Stocks',
  '501D': 'Bonds',
  '501E': 'Money Market',
};

// Map section IDs to their routes
const subCategoryRoutes: Record<string, string> = {
  '501A': '/category/financialplans/financialplanner',
  '501B': '/category/financialplans/investments',
  '501C': '/category/financialplans/stocks',
  '501D': '/category/financialplans/bonds',
  '501E': '/category/financialplans/moneymarket',
};

// Map question IDs to their section IDs
const questionToSectionMap: Record<string, string> = {};

// Initialize the question to section mapping
Object.entries(financialPlansData).forEach(([, questions]) => {
  questions.forEach((question: any) => {
    if (question.sectionId) {
      questionToSectionMap[question.id] = question.sectionId;
    }
  });
});

export default function FinancialPlansReview() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { canAccessCategory, userAccess } = useAccessControl();
  const [topics, setTopics] = useState<Array<{
    id: string;
    title: string;
    subtitle?: string;
    data: string;
    onEdit: () => void;
  }>>([]);

  // Get data from Redux store using selectors
  const userInputs = useAppSelector((state: any) => state.financialPlans.userInputs) as ReduxUserInput[];
  const loading = useAppSelector((state: any) => state.financialPlans.loading);
  const error = useAppSelector((state: any) => state.financialPlans.error);

  // Fallback user info if not authenticated
  const userInfo = createUserInfo(user);

  // Fetch user inputs when component mounts using owner ID
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in FinancialPlansReview component');
          }
        } catch (error) {
          console.error('Error fetching owner ID in FinancialPlansReview component:', error);
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  // Process user inputs to create topics for review
  useEffect(() => {
    if (!user?.id) {
      return;
    }

    if (userInputs.length > 0 && !loading) {
      // Transform the data for the review page
      const allTopics: Array<{
        id: string;
        title: string;
        subtitle?: string;
        data: string;
        onEdit: () => void;
      }> = [];

      // Process all user inputs
      userInputs.forEach((userInput: ReduxUserInput) => {
        // Process answers by section
        userInput.answersBySection.forEach((section) => {
          section.answers.forEach((answer) => {
            // Find the original question from our data
            const questionId = answer.originalQuestionId;
            const sectionId = questionToSectionMap[questionId];

            // Find the question data from the 501 array
            const questionData = financialPlansData['501']?.find((q: any) => q.id === questionId);

            if (questionData && answer.answer && answer.answer.trim() !== '') {
              // Format the answer for display
              let displayAnswer = answer.answer;
              let shouldDisplay = true;

              if (questionData.type === 'boolean') {
                displayAnswer = answer.answer === 'yes' ? 'Yes' : 'No';
              } else if (questionData.type === 'contacts' || questionData.isContacts) {
                // Handle contacts specially - following ImportantContacts pattern
                try {
                  const parsed = JSON.parse(answer.answer);
                  if (Array.isArray(parsed)) {
                    // Filter out empty contacts
                    const validContacts = parsed.filter((c: any) =>
                      c && (c.name?.trim() || c.phone?.trim() || c.info?.trim())
                    );
                    if (validContacts.length > 0) {
                      displayAnswer = validContacts
                        .map((c: any) => {
                          const phoneNumber = c.phone || c.info || '';
                          return `${c.name || ''}${phoneNumber ? ` (${phoneNumber})` : ''}`;
                        })
                        .filter(Boolean)
                        .join(', ');
                    } else {
                      shouldDisplay = false; // Don't show empty contact arrays
                    }
                  } else if (parsed && typeof parsed === 'object' && (parsed.name || parsed.phone || parsed.info)) {
                    // Single contact object
                    const phoneNumber = parsed.phone || parsed.info || '';
                    displayAnswer = `${parsed.name || ''}${phoneNumber ? ` (${phoneNumber})` : ''}`;
                  } else {
                    shouldDisplay = false; // Don't show invalid contact data
                  }
                } catch {
                  // If parsing fails, check if it's just "[]" string
                  if (answer.answer.trim() === '[]' || answer.answer.trim() === '') {
                    shouldDisplay = false;
                  }
                }
              }

              // Only add to topics if we should display it
              if (shouldDisplay) {
                allTopics.push({
                  id: questionId,
                  title: questionData.text,
                  subtitle: `Section: ${sectionTitles[sectionId] || sectionId}`,
                  data: displayAnswer,
                  onEdit: () => {
                    // Navigate to the appropriate section page with question ID as a parameter
                    const route = subCategoryRoutes[sectionId];
                    if (route) {
                      navigate(`${route}?questionId=${questionId}`);
                    }
                  }
                });
              }
            }
          });
        });
      });

      setTopics(allTopics);
    }
  }, [userInputs, loading, navigate, user]);

  if (loading) {
    return <div className="flex justify-center items-center h-screen">Loading your answers...</div>;
  }

  if (error) {
    return <div className="flex justify-center items-center h-screen text-red-500">{error}</div>;
  }

  if (!user?.id) {
    return <div className="flex justify-center items-center h-screen text-red-500">You must be logged in to view your answers</div>;
  }

  // Check if user can access Insurance category (next category in flow)
  const canAccessInsurance = canAccessCategory(CATEGORIES.INSURANCE);

  // Handle navigation to Insurance
  const handleContinueToInsurance = () => {
    if (canAccessInsurance) {
      navigate('/category/ownershipinfo');
    } else {
      // For temporary_key users, show upgrade message or navigate to subscription
      if (userAccess?.subscriptionType === 'temporary_key') {
        // You can either show a toast message or navigate to subscription page
        navigate('/subscription');
      }
    }
  };

  return (
    <div className="flex flex-col items-center">
      <CategoryReviewPage
        categoryTitle="Financial Plans"
        infoTitle="How to edit your information"
        infoDescription="Now, you are about to enter details about your financial plans, investment accounts, and financial planning information to be passed on to your family members. Each section has several questions. Fill out as much as you can/like. You can always come back to fill out more information later."
        topics={topics}
        user={userInfo}
        onPrint={() => window.print()}
        afterTopics={
          <button
            onClick={handleContinueToInsurance}
            className={`px-8 py-3 rounded-md transition-colors duration-200 shadow-md font-semibold text-md mt-1 mb-1 ${
              canAccessInsurance
                ? 'bg-[#2BCFD5] text-white hover:bg-[#1F4168]'
                : 'bg-gray-400 text-white cursor-not-allowed'
            }`}
            disabled={!canAccessInsurance}
          >
            {canAccessInsurance
              ? 'Continue to Ownership Information'
              : 'Upgrade to Continue'
            }
          </button>
        }
      />
    </div>
  );
}
