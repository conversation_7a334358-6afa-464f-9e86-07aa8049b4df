# Set default behavior to automatically normalize line endings
* text=auto

# Explicitly declare text files you want to always be normalized and converted
# to native line endings on checkout
*.ts text
*.tsx text
*.js text
*.jsx text
*.json text
*.css text
*.scss text
*.html text
*.md text
*.yml text
*.yaml text
*.xml text
*.txt text

# Declare files that will always have CRLF line endings on checkout
*.bat text eol=crlf

# Declare files that will always have LF line endings on checkout
*.sh text eol=lf

# Denote all files that are truly binary and should not be modified
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg binary
*.woff binary
*.woff2 binary
*.ttf binary
*.eot binary
*.pdf binary
*.zip binary
*.tar.gz binary 