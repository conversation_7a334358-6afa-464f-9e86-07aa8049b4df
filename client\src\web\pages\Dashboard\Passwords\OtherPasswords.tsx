import { useState, useEffect, useMemo } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Formik, Form, ErrorMessage, Field } from "formik";
import * as Yup from 'yup';
import { useAuth } from '@/contexts/AuthContext';
import { generateObjectId, convertUserInputToFormValues } from '@/services/userInputService';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  UserInput,
  fetchUserInputs,
  saveUserInput,
  selectLoading,
  selectQuestionsBySubcategoryId,
  selectUserInputsBySubcategoryId,
  updateUserInput,
  selectError,
} from '@/store/slices/passwordsSlice';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Plus, Trash2, Eye, EyeOff } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import passwordsData from '@/data/Passwords.json';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import SubCategoryHeader from '@/web/components/Global/SubCategoryHeader';
import SubCategoryTabs from '@/web/components/Global/SubCategoryTabs';
import SubCategoryTitle from '@/web/components/Global/SubCategoryTitle';
import Footer from '@/web/components/Layout/Footer';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import GoodToKnowBox from '@/web/components/Global/GoodToKnowBox';
import SubCategoryFooterNav from '@/web/components/Global/SubCategoryFooterNav';
import { createUserInfo } from '@/utils/avatarUtils';
import { QuestionItem, Question, buildValidationSchema, generateInitialValues, handleDependentAnswers } from '@/web/components/Category/Passwords/FormFields';
import ScrollToQuestion from '@/web/components/Category/Passwords/ScrollToQuestion';

const OTHER_PASSWORDS_SECTION_ID = '1204';
const DROPDOWN_QUESTION_ID = 'p1';
const YESNO_QUESTION_ID = 'p2';

interface PasswordEntry {
  serviceName: string;
  customServiceName?: string;
  displayName?: string;
  accounts: AccountEntry[];
}

interface AccountEntry {
  username: string;
  password: string;
}

// Update the type definitions at the top
type FormValues = Record<string, string | string[]>;

export default function OtherPasswords() {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const [formError, setError] = useState<string | null>(null);
  const [selectedServices, setSelectedServices] = useState<PasswordEntry[]>([]);
  const [duplicateServiceError, setDuplicateServiceError] = useState<string>('');
  const [selectedService, setSelectedService] = useState<string>('');
  const [customServiceName, setCustomServiceName] = useState<string>('');
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({});

  // Create memoized selectors to prevent unnecessary re-renders
  const userInputsSelector = useMemo(() => selectUserInputsBySubcategoryId('1204A'), []);
  const questionsSelector = useMemo(() => selectQuestionsBySubcategoryId(OTHER_PASSWORDS_SECTION_ID), []);

  // Get data from Redux store
  const userInputs = useAppSelector(userInputsSelector);
  const isLoadingRedux = useAppSelector(selectLoading);
  const reduxError = useAppSelector(selectError);
  const storeQuestions = useAppSelector(questionsSelector);

  // Get the questionId from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const targetQuestionId = queryParams.get('questionId');
  const fromReview = queryParams.get('fromReview');
  const serviceIndex = queryParams.get('serviceIndex');

  // Get dropdown options from the question
  const dropdownQuestion = storeQuestions.find(q => q.id === DROPDOWN_QUESTION_ID);
  const dropdownOptions = dropdownQuestion?.options || [];

  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchUserAnswers = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      }
    };

    fetchUserAnswers();
  }, [dispatch, user]);

  // State for processed data
  const [savedAnswers, setSavedAnswers] = useState<Record<string, string | string[]>>({});
  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const [dataLoaded, setDataLoaded] = useState(false);

  // Process user inputs to get saved answers
  useEffect(() => {
    if (isLoadingRedux) {
      return;
    }

    if (userInputs.length === 0) {
      setSavedAnswers({});
      setSelectedServices([]);
      setExistingInputId(null);
      setDataLoaded(true);
      return;
    }

    const userInput = userInputs.find((input: UserInput) => input.originalSubCategoryId === '1204A');

    if (!userInput) {
      setSavedAnswers({});
      setSelectedServices([]);
      setExistingInputId(null);
      setDataLoaded(true);
      return;
    }

    const formValues = convertUserInputToFormValues(userInput as any);

    // Load services from saved data
    const services: PasswordEntry[] = [];
    userInput.answersBySection.forEach(section => {
      section.answers.forEach(answer => {
        if (answer.originalQuestionId === DROPDOWN_QUESTION_ID) {
          try {
            const parsedData = JSON.parse(answer.answer);
            if (Array.isArray(parsedData)) {
              parsedData.forEach((item: any) => {
                if (typeof item === 'object' && item.serviceName) {
                  const serviceEntry: PasswordEntry = {
                    serviceName: item.serviceName,
                    accounts: item.accounts || [{ username: item.username || '', password: item.password || '' }]
                  };
                  if (item.serviceName === 'Other' && item.customServiceName) {
                    serviceEntry.customServiceName = item.customServiceName;
                    serviceEntry.displayName = item.customServiceName;
                  } else if (item.displayName) {
                    serviceEntry.displayName = item.displayName;
                  } else {
                    serviceEntry.displayName = item.serviceName;
                  }
                  services.push(serviceEntry);
                }
              });
            }
          } catch (error) {
            console.error('Error parsing service data:', error);
          }
        }
      });
    });

    setSavedAnswers(formValues);
    setSelectedServices(services);
    setExistingInputId(userInput._id || null);
    setDataLoaded(true);
  }, [userInputs, isLoadingRedux]);

  // Handle scroll to question when navigating from review
  useEffect(() => {
    console.log('OtherPasswords: Scroll to question effect triggered', {
      isLoadingRedux,
      targetQuestionId,
      fromReview,
      storeQuestionsLength: storeQuestions.length
    });

    if (!isLoadingRedux && targetQuestionId && fromReview && storeQuestions.length > 0) {
      // Find the question in our store to make sure it exists
      const questionExists = storeQuestions.find(q => q.id === targetQuestionId);
      console.log('OtherPasswords: Question exists:', questionExists);

      if (questionExists) {
        console.log('OtherPasswords: Scrolling to question:', `question-${targetQuestionId}`);
        setTimeout(() => {
          scrollToElement(`question-${targetQuestionId}`, 'bg-yellow-100', 5);
        }, 1000);
      }
    }
  }, [isLoadingRedux, targetQuestionId, fromReview, storeQuestions.length]);

  // Handle service highlighting when navigating from review page
  useEffect(() => {
    console.log('OtherPasswords: Service scroll effect triggered', {
      isLoadingRedux,
      serviceIndex,
      selectedServicesLength: selectedServices.length,
      fromReview,
      dataLoaded
    });

    if (!isLoadingRedux && serviceIndex !== null && selectedServices.length > 0 && fromReview && dataLoaded) {
      const index = parseInt(serviceIndex);
      console.log('OtherPasswords: Parsed service index:', index);

      if (index >= 0 && index < selectedServices.length) {
        console.log('OtherPasswords: Scrolling to service:', `service-${index}`);
        setTimeout(() => {
          scrollToElement(`service-${index}`, 'bg-blue-50', 5);
        }, 1000);
      }
    }
  }, [isLoadingRedux, serviceIndex, selectedServices.length, fromReview, dataLoaded]);



  const addService = () => {
    if (!selectedService) {
      return;
    }
    
    if (selectedService === 'Other' && !customServiceName.trim()) {
      return;
    }
    
    // Check for duplicates
    const isDuplicate = selectedServices.some(service => {
      if (selectedService === 'Other' && customServiceName) {
        return service.serviceName === 'Other' && service.customServiceName === customServiceName;
      }
      return service.serviceName === selectedService;
    });

    if (isDuplicate) {
      const displayName = selectedService === 'Other' && customServiceName ? customServiceName : selectedService;
      setDuplicateServiceError(`${displayName} is already added`);
      return;
    }

    const newService: PasswordEntry = {
      serviceName: selectedService,
      accounts: [{ username: '', password: '' }]
    };

    if (selectedService === 'Other' && customServiceName) {
      newService.customServiceName = customServiceName;
      newService.displayName = customServiceName;
    } else {
      newService.displayName = selectedService;
    }
    
    setSelectedServices(prev => [...prev, newService]);
    setSelectedService('');
    setCustomServiceName('');
    setDuplicateServiceError('');
  };

  // Helper function to scroll to element with retry
  const scrollToElement = (elementId: string, highlightClass: string, maxRetries = 5) => {
    console.log('OtherPasswords: scrollToElement called for:', elementId);
    let retryCount = 0;

    const tryScroll = () => {
      const element = document.getElementById(elementId);
      console.log('OtherPasswords: Element found:', !!element, 'Retry count:', retryCount);

      if (element) {
        console.log('OtherPasswords: Scrolling to element:', elementId);
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        element.classList.add(highlightClass, 'border-2', 'shadow-lg');
        setTimeout(() => {
          element.classList.remove(highlightClass, 'border-2', 'shadow-lg');
        }, 3000);
        return true;
      } else if (retryCount < maxRetries) {
        retryCount++;
        console.log('OtherPasswords: Element not found, retrying in 500ms. Retry:', retryCount);
        setTimeout(tryScroll, 500);
      } else {
        console.log('OtherPasswords: Element not found after max retries:', elementId);
      }
      return false;
    };

    tryScroll();
  };

  const removeService = (index: number) => {
    const newServices = selectedServices.filter((_, i) => i !== index);
    setSelectedServices(newServices);
    setDuplicateServiceError('');
  };

  const addAccount = (serviceIndex: number) => {
    const newServices = [...selectedServices];
    newServices[serviceIndex].accounts.push({ username: '', password: '' });
    setSelectedServices(newServices);
  };

  const removeAccount = (serviceIndex: number, accountIndex: number) => {
    const newServices = [...selectedServices];
    if (newServices[serviceIndex].accounts.length > 1) {
      newServices[serviceIndex].accounts.splice(accountIndex, 1);
      setSelectedServices(newServices);
    }
  };

  const updateAccount = (serviceIndex: number, accountIndex: number, field: 'username' | 'password', value: string) => {
    const newServices = [...selectedServices];
    newServices[serviceIndex].accounts[accountIndex][field] = value;
    setSelectedServices(newServices);
  };

  const togglePasswordVisibility = (serviceIndex: number, accountIndex: number) => {
    const key = `${serviceIndex}-${accountIndex}`;
    setShowPasswords(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Generate initial values using saved answers
  const baseInitialValues: FormValues = {};
  storeQuestions.forEach(q => {
    if (q.type === 'yesno') {
      baseInitialValues[q.id] = '';
    }
  });

  // Ensure saved answers are properly loaded, especially for yes/no questions
  const initialValues: FormValues = {
    ...baseInitialValues,
    ...savedAnswers,
    // Explicitly set the yes/no question value if it exists in saved answers
    [YESNO_QUESTION_ID]: savedAnswers[YESNO_QUESTION_ID] || ''
  };



  const validationSchema = Yup.object().shape({
    [YESNO_QUESTION_ID]: Yup.string().required('Please select an option'),
  });

  // Show loading while fetching data or if questions are not loaded or data is not processed
  if (isLoadingRedux || storeQuestions.length === 0 || !dataLoaded) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }

  if (formError || reduxError) {
    return (
      <div className="flex flex-col min-h-screen">
        <div className="container mx-auto px-4 py-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">{formError || reduxError}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen">
      <SubCategoryHeader
        title="Passwords"
        backTo="/dashboard"
        user={{
          name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'User',
          email: user?.email || '',
          avatar: createUserInfo(user).avatar
        }}
      />
      <SubCategoryTabs tabs={categoryTabsConfig.passwords} />
      <div className="container mx-auto px-6">
        <SubCategoryTitle
          mainCategory="Passwords"
          category="Other Passwords"
          description="Store passwords for various services and accounts that you want to share with your loved ones."
        />
      </div>
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <Formik
                key={`formik-${existingInputId || 'new'}-${JSON.stringify(savedAnswers)}`}
                initialValues={initialValues}
                validationSchema={validationSchema}
                enableReinitialize={true}
                onSubmit={async (values, { setSubmitting }) => {
                  try {
                    if (!user || !user.id) {
                      throw new Error('You must be logged in to save answers');
                    }

                    // Prepare answers for saving
                    const answersBySection = [{
                      originalSectionId: '1204A',
                      isCompleted: true,
                      answers: [
                        // Yes/No answer
                        {
                          index: 0,
                          originalQuestionId: YESNO_QUESTION_ID,
                          question: 'Do you want to save any other passwords?',
                          type: 'boolean',
                          answer: values[YESNO_QUESTION_ID]
                        },
                        // Service selection answer (only if they answered yes)
                        ...(values[YESNO_QUESTION_ID] === 'yes' ? [{
                          index: 1,
                          originalQuestionId: DROPDOWN_QUESTION_ID,
                          question: 'Select the services/accounts you want to store passwords for:',
                          type: 'choice',
                          answer: JSON.stringify(selectedServices)
                        }] : [])
                      ]
                    }];

                    const userData = {
                      userId: user.id,
                      categoryId: generateObjectId(),
                      originalCategoryId: '12',
                      subCategoryId: generateObjectId(),
                      originalSubCategoryId: '1204A',
                      answersBySection: answersBySection
                    };

                    if (existingInputId) {
                      await dispatch(updateUserInput({
                        id: existingInputId,
                        userData
                      })).unwrap();
                    } else {
                      await dispatch(saveUserInput(userData)).unwrap();
                    }

                    navigate('/category/passwords/review');
                    setSubmitting(false);
                  } catch (err) {
                    setError("Failed to save your answers. Please try again.");
                    setSubmitting(false);
                  }
                }}
              >
                {({ values, isSubmitting }) => (
                  <Form>
                    <div className="space-y-6">
                      {/* Yes/No Questions */}
                      <div className="space-y-4 mt-8">
                        {storeQuestions
                          .filter(q => q.type === 'yesno')
                          .map(q => (
                            <div key={q.id} id={`question-${q.id}`}>
                              <Label className="block font-medium text-gray-700 mb-2">
                                {q.text}
                              </Label>
                              <div className="flex space-x-4">
                                {["yes", "no"].map(option => (
                                  <label
                                    key={option}
                                    className={
                                      "flex-1 py-2 px-4 border rounded-xl text-center cursor-pointer " +
                                      (values[q.id] === option
                                        ? "bg-[#2BCFD5] text-white border-[#2BCFD5]"
                                        : "bg-gray-50 hover:bg-[#25b6bb] hover:text-white")
                                    }
                                    style={{ transition: "all 0.2s" }}
                                  >
                                    <Field type="radio" name={q.id} value={option} className="hidden" />
                                    {option.charAt(0).toUpperCase() + option.slice(1)}
                                  </label>
                                ))}
                              </div>
                              <ErrorMessage name={q.id} component="div" className="text-red-500 text-sm mt-1" />
                            </div>
                          ))}
                      </div>

                      {/* Service Selection - Only show if user answered "Yes" */}
                      {(values[YESNO_QUESTION_ID] === 'yes') && (
                        <div className="space-y-4 mt-8">
                          {/* Service Selection */}
                          <div className="bg-white rounded-xl p-4 mb-6 shadow-sm" id={`question-${DROPDOWN_QUESTION_ID}`}>
                            <Label className="text-base font-medium text-gray-900 mb-4 block">
                              Select services to store passwords for:
                            </Label>
                            
                            <div className="space-y-4">
                              <Select value={selectedService} onValueChange={setSelectedService} key="service-select">
                                <SelectTrigger>
                                  <SelectValue placeholder="Select a service..." />
                                </SelectTrigger>
                                <SelectContent>
                                  {dropdownOptions.map((option: any) => (
                                    <SelectItem key={option.value} value={option.value}>
                                      {option.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>

                              {selectedService === 'Other' && (
                                <Input
                                  placeholder="Enter custom service name..."
                                  value={customServiceName}
                                  onChange={(e) => setCustomServiceName(e.target.value)}
                                />
                              )}

                              <Button
                                type="button"
                                onClick={addService}
                                disabled={!selectedService || (selectedService === 'Other' && !customServiceName.trim())}
                                className="w-full bg-[#2BCFD5] hover:bg-[#25b5ba] text-white"
                              >
                                <Plus className="w-4 h-4 mr-2" />
                                Add Service
                              </Button>

                              {duplicateServiceError && (
                                <p className="text-red-500 text-sm">{duplicateServiceError}</p>
                              )}
                            </div>
                          </div>

                          {/* Selected Services */}
                          {selectedServices.length > 0 && (
                            <div className="space-y-4 mb-6">
                              <h3 className="text-lg font-medium text-gray-900">Your Password Entries:</h3>
                              {selectedServices.map((service, serviceIndex) => (
                                <div key={`${service.serviceName}-${serviceIndex}`} className="bg-white rounded-xl p-4 shadow-sm" id={`service-${serviceIndex}`}>
                                  <div className="flex justify-between items-center mb-4">
                                    <h4 className="text-lg font-medium text-gray-900">{service.displayName}</h4>
                                    <Button
                                      type="button"
                                      variant="outline"
                                      size="sm"
                                      onClick={() => removeService(serviceIndex)}
                                      className="text-red-600 hover:text-red-700"
                                    >
                                      <Trash2 className="w-4 h-4" />
                                    </Button>
                                  </div>
                                  
                                  <div className="space-y-3">
                                    {service.accounts.map((account, accountIndex) => (
                                      <div key={accountIndex} className="p-3 bg-gray-50 rounded-lg">
                                        <div className="space-y-3">
                                          <div>
                                            <Label className="text-sm font-medium text-gray-700">Username/Email</Label>
                                            <Input
                                              placeholder="Enter username or email..."
                                              value={account.username}
                                              onChange={(e) => updateAccount(serviceIndex, accountIndex, 'username', e.target.value)}
                                            />
                                          </div>
                                          <div>
                                            <Label className="text-sm font-medium text-gray-700">Password</Label>
                                            <div className="relative">
                                              <Input
                                                type={showPasswords[`${serviceIndex}-${accountIndex}`] ? "text" : "password"}
                                                placeholder="Enter password..."
                                                value={account.password}
                                                onChange={(e) => updateAccount(serviceIndex, accountIndex, 'password', e.target.value)}
                                                className="pr-10"
                                              />
                                              <button
                                                type="button"
                                                onClick={() => togglePasswordVisibility(serviceIndex, accountIndex)}
                                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                                              >
                                                {showPasswords[`${serviceIndex}-${accountIndex}`] ? (
                                                  <EyeOff className="w-4 h-4" />
                                                ) : (
                                                  <Eye className="w-4 h-4" />
                                                )}
                                              </button>
                                            </div>
                                          </div>
                                          {service.accounts.length > 1 && (
                                            <Button
                                              type="button"
                                              variant="outline"
                                              size="sm"
                                              onClick={() => removeAccount(serviceIndex, accountIndex)}
                                              className="text-red-600 hover:text-red-700 w-full"
                                            >
                                              <Trash2 className="w-4 h-4 mr-2" />
                                              Remove Account
                                            </Button>
                                          )}
                                        </div>
                                      </div>
                                    ))}
                                    
                                    <Button
                                      type="button"
                                      variant="outline"
                                      size="sm"
                                      onClick={() => addAccount(serviceIndex)}
                                      className="w-full"
                                    >
                                      <Plus className="w-4 h-4 mr-2" />
                                      Add Another Account
                                    </Button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      )}

                      {/* Save Button */}
                      <div className="flex justify-between mt-8">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => navigate('/category/passwords/review')}
                        >
                          Back
                        </Button>
                        <Button
                          type="submit"
                          disabled={isSubmitting || (values[YESNO_QUESTION_ID] === 'yes' && selectedServices.length === 0)}
                          className="bg-[#2BCFD5] hover:bg-[#25b5ba] text-white"
                        >
                          {isSubmitting ? 'Saving...' : 'Save & Continue'}
                        </Button>
                      </div>

                      <GoodToKnowBox
                        title="Editing my Answers"
                        description="Each topic below is a part of your password services, with questions to help you provide important information for you and your loved ones. Click any topic to answer the questions at your own pace—we'll save everything for you."
                      />
                      <SubCategoryFooterNav
                        leftLabel="Passwords Info"
                        leftTo="/category/passwords/info"
                        rightLabel="Passwords Review"
                        rightTo="/category/passwords/review"
                      />
                    </div>
                  </Form>
                )}
              </Formik>
            </div>
          </div>
          <div>
            <SearchPanel />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
} 