import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import CategoryReviewPage from '@/web/components/Category/CategoryReviewPage';
import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import { useAuth } from '@/contexts/AuthContext';
import bankingData from '@/data/banking.json';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  UserInput as ReduxUserInput
} from '@/store/slices/bankingInformationSlice';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

// Define interfaces for the data structure
interface Answer {
  index: number;
  questionId?: string;
  originalQuestionId: string;
  question: string;
  type: string;
  answer: string;
}

interface SectionAnswers {
  originalSectionId: string;
  isCompleted: boolean;
  answers: Answer[];
}

interface UserInput {
  userId: string;
  categoryId: string;
  originalCategoryId: string;
  subCategoryId: string;
  originalSubCategoryId: string;
  answersBySection: SectionAnswers[];
}

// Map section IDs to their routes
const subCategoryRoutes: Record<string, string> = {
  '405': '/category/bankinginformation/bankdetails',
};

// Map question IDs to their section IDs
const questionToSubcategoryMap: Record<string, string> = {};

// Initialize the question to section mapping
Object.entries(bankingData).forEach(([subcategoryId, questions]) => {
  questions.forEach((question: any) => {
    questionToSubcategoryMap[question.id] = subcategoryId;
  });
});

export default function BankingInformationReview() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [topics, setTopics] = useState<Array<{
    id: string;
    title: string;
    subtitle?: string;
    data: string;
    onEdit: () => void;
  }>>([]);

  // Get data from Redux store
  const userInputs = useAppSelector((state: any) => state.bankingInformation.userInputs) as ReduxUserInput[];
  const isLoading = useAppSelector((state: any) => state.bankingInformation.loading);
  const error = useAppSelector((state: any) => state.bankingInformation.error);

  // Fallback user info if not authenticated
  const userInfo = {
    name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
    email: user?.email || '<EMAIL>',
    avatar: avatar
  };

  // Fetch user inputs when component mounts using owner ID
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in BankingInformationReview component');
          }
        } catch (error) {
          console.error('Error fetching owner ID in BankingInformationReview component:', error);
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  // Process user inputs to create topics for review page
  useEffect(() => {
    if (!isLoading && userInputs.length > 0) {
      // Transform the data for the review page
      const allTopics: Array<{
        id: string;
        title: string;
        subtitle?: string;
        data: string;
        onEdit: () => void;
      }> = [];

      // Process all user inputs
      userInputs.forEach((userInput: ReduxUserInput) => {
        userInput.answersBySection.forEach((section) => {
          section.answers.forEach((answer) => {
            // Find the original question from our data
            const questionId = answer.originalQuestionId;
            const subcategoryId = questionToSubcategoryMap[questionId];
            const allQuestions = bankingData[subcategoryId as keyof typeof bankingData];
            const questionData = allQuestions?.find((q: any) => q.id === questionId);

            if (questionData) {
              let displayAnswer = answer.answer;
              
              // Handle choice type answers
              if (questionData.type === 'choice') {
                // If the answer is "Other", look for additional details
                if (answer.answer === "Other") {
                  const otherAnswer = section.answers.find(a => a.originalQuestionId === `${answer.originalQuestionId}_other`);
                  if (otherAnswer) {
                    displayAnswer = `Other: ${otherAnswer.answer}`;
                  }
                }
              }

              allTopics.push({
                id: questionId,
                title: questionData.text,
                subtitle: `Section: ${section.originalSectionId}`,
                data: displayAnswer,
                onEdit: () => {
                  // Navigate to the appropriate section page with question ID as a parameter
                  const route = subCategoryRoutes[subcategoryId];
                  if (route) {
                    navigate(`${route}?questionId=${questionId}`);
                  }
                }
              });
            }
          });
        });
      });

      setTopics(allTopics);
    }
  }, [userInputs, isLoading, navigate]);

  if (isLoading) {
    return <div className="p-4 text-center">Loading your banking information...</div>;
  }

  if (error) {
    return (
      <div className="p-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    );
  }

  if (!user || !user.id) {
    return (
      <div className="p-4">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-yellow-600">You must be logged in to view your banking information</p>
        </div>
      </div>
    );
  }

  if (userInputs.length === 0 && !isLoading) {
    return (
      <div className="p-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-blue-600">No banking information found. Please complete some questions first.</p>
        </div>
      </div>
    );
  }

  return (
    <CategoryReviewPage
      categoryTitle="Banking Information"
      infoTitle="How to edit your banking information"
      infoDescription="Review the details about your banking information. Click Edit on any item to update it."
      topics={topics}
      onPrint={() => window.print()}
      user={userInfo}
      afterTopics={
        <button
          onClick={() => navigate('/category/passwords')}
          className="px-8 py-3 bg-[#2BCFD5] text-white rounded-md hover:bg-[#1F4168] transition-colors duration-200 shadow-md font-semibold text-md mt-1 mb-1"
        >
          Continue to Passwords
        </button>
      }
    />
  );
}
