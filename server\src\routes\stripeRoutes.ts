import express from "express"
import {customerDetails,SubscribeStripe,stripeWebhooks,successPayment} from "../controller/stripeController"
import { corsDebugMiddleware, testCorsEndpoint } from "../utils/corsDebug"
import Stripe from "stripe"
import SubscribedPlan from '../models/SubscribedPlan';
import Owner from '../models/Owner';
import PricingPlan from '../models/PricingPlan';
import User from '../models/User';
import mongoose from 'mongoose';
// import { SubscribeStripe } from "../controller/stripeController";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string)

const router = express.Router()

// Add CORS debug middleware for development
if (process.env.NODE_ENV === 'development') {
  router.use(corsDebugMiddleware);
}

// Test endpoint to verify CORS
router.get('/test-cors', testCorsEndpoint);

// Regular JSON endpoints
router.get('/subscribe', SubscribeStripe as express.RequestHandler)
router.get('/success',successPayment as express.RequestHandler)
router.get('/customer/:customerId',customerDetails as express.RequestHandler)

// Stripe webhook endpoint - uses raw body stored by body-parser for signature verification
router.post('/',express.raw({ type: 'application/json' }), stripeWebhooks as express.RequestHandler)

// Manual webhook endpoint for local testing - simulates Stripe payment completion
router.post('/manual-webhook', (async (req: express.Request, res: express.Response) => {
    try {
        // Only allow in development mode or when NODE_ENV is not set (local development)
        if (process.env.NODE_ENV === 'production') {
            return res.status(403).json({ error: 'This endpoint is not available in production mode' });
        }

        const { email, planType, sessionId } = req.body;

        if (!email || !planType) {
            return res.status(400).json({ error: 'Email and planType are required' });
        }

        console.log('🔧 Manual webhook triggered for:', { email, planType, sessionId });

        // Find the pricing plan
        const plan = await PricingPlan.findOne({ type: planType });
        if (!plan) {
            return res.status(400).json({ error: `Plan type '${planType}' not found` });
        }

        // Check if owner already exists
        let owner = await Owner.findOne({ email: email.toLowerCase() });

        if (!owner) {
            // Create temporary owner record (simulating webhook behavior)
            console.log('Creating temporary owner for email:', email);
            owner = new Owner({
                email: email.toLowerCase(),
                externalUser: false,
                username: '',
                firstName: '',
                lastName: '',
                userId: new mongoose.Types.ObjectId(), // Temporary placeholder
                stripeSessionId: sessionId || `manual_${Date.now()}`
            });
            await owner.save();
            console.log('Created temporary owner with ID:', owner._id);
        } else {
            // Update existing owner with session ID if provided
            if (sessionId) {
                owner.stripeSessionId = sessionId;
                await owner.save();
            }
        }

        // Check if subscription already exists
        let subscription = await SubscribedPlan.findOne({ ownerId: owner._id });

        if (!subscription) {
            console.log('Creating new subscription...');
            subscription = new SubscribedPlan({
                planId: plan._id,
                ownerId: owner._id,
                currentPlan: plan._id,
                previousPlans: []
            });
            await subscription.save();
            console.log('New subscription saved:', subscription._id);

            // Update owner with subscription ID
            owner.subscribedPlanId = subscription._id as mongoose.Types.ObjectId;
            await owner.save();
            console.log('Owner updated with subscription ID');
        } else {
            console.log('Subscription already exists:', subscription._id);
        }

        // Update any existing users with this owner's subscription type
        const users = await User.find({ ownerId: owner._id });
        if (users.length > 0) {
            await User.updateMany(
                { ownerId: owner._id },
                {
                    subscriptionType: plan.type,
                    pricingPlan: plan.type, // Add pricingPlan field for frontend compatibility
                    allowedCategoryId: null // Reset allowed category for new subscription type
                }
            );
            console.log('Updated', users.length, 'users with subscription type:', plan.type);
        }

        res.json({
            success: true,
            message: 'Manual webhook processed successfully',
            data: {
                ownerId: owner._id,
                subscriptionId: subscription._id,
                planType: plan.type,
                email: email
            }
        });

    } catch (error) {
        console.error('Manual webhook error:', error);
        res.status(500).json({ error: 'Failed to process manual webhook' });
    }
}) as express.RequestHandler);

// Check subscription status for a user (no auth required for testing)
router.get('/check-subscription/:ownerId', async (req: express.Request, res: express.Response) => {
    try {
        const { ownerId } = req.params;
        const owner = await Owner.findById(ownerId);
        const subscription = await SubscribedPlan.findOne({ ownerId });

        res.json({
            owner: {
                _id: owner?._id,
                subscribedPlanId: owner?.subscribedPlanId,
                email: owner?.email
            },
            subscription: subscription ? {
                _id: subscription._id,
                planId: subscription.planId,
                currentPlan: subscription.currentPlan,
                ownerId: subscription.ownerId
            } : null
        });
    } catch (error) {
        console.error('Error checking subscription:', error);
        res.status(500).json({ error: 'Failed to check subscription' });
    }
});

// Manual test endpoint for development - simulate webhook processing
router.post('/test-webhook', (async (req: express.Request, res: express.Response) => {
    if (process.env.NODE_ENV !== 'development') {
        return res.status(403).json({ error: 'Forbidden' });
    }
    try {
        const { sessionId } = req.body;
        if (!sessionId) {
            return res.status(400).json({ error: 'Session ID required' });
        }
        
        // Retrieve the session from Stripe
        const session = await stripe.checkout.sessions.retrieve(sessionId, {
            expand: ['subscription']
        });
        
        // Simulate webhook processing
        const metadata = session.metadata || {};
        const ownerId = metadata.ownerId;
        const planId = metadata.planId;
        
        if (ownerId && planId) {
            const existing = await SubscribedPlan.findOne({ ownerId });
            if (!existing) {
                const subscription = new SubscribedPlan({
                    planId,
                    ownerId,
                    currentPlan: planId,
                    previousPlans: []
                });
                await subscription.save();
                await Owner.findByIdAndUpdate(ownerId, { subscribedPlanId: subscription._id });
                res.json({ success: true, message: 'Subscription created manually', subscriptionId: subscription._id });
            } else {
                res.json({ success: true, message: 'Subscription already exists', subscriptionId: existing._id });
            }
        } else {
            res.status(400).json({ error: 'Missing metadata in session' });
        }
    } catch (error) {
        console.error('Manual webhook test error:', error);
        res.status(500).json({ error: 'Failed to process webhook manually' });
    }
}) as express.RequestHandler);

export default router;