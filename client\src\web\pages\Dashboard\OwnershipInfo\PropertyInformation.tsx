
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import { convertUserInputToFormValues, generateObjectId } from '@/services/userInputService';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  UserInput,
  fetchUserInputs,
  saveUserInput,
  selectLoading,
  selectQuestionsBySubcategoryId,
  selectUserInputsBySubcategoryId,
  updateUserInput,
} from '@/store/slices/ownershipInfoSlice';
import {
  QuestionItem,
  buildValidationSchema,
  generateInitialValues,
  handleDependentAnswers,
  cleanDependentAnswers
} from '@/web/components/Category/OwnershipInfo/FormFields';

import GoodToKnowBox from '@/web/components/Global/GoodToKnowBox';
import SubCategoryFooterNav from '@/web/components/Global/SubCategoryFooterNav';
import SubCategoryHeader from '@/web/components/Global/SubCategoryHeader';
import SubCategoryTabs from '@/web/components/Global/SubCategoryTabs';
import SubCategoryTitle from '@/web/components/Global/SubCategoryTitle';
import AppHeader from '@/web/components/Layout/AppHeader';
import Footer from '@/web/components/Layout/Footer';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import { Form, Formik, FormikHelpers } from 'formik';
import { useEffect, useMemo, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import * as Yup from 'yup';
import { createUserInfo } from '@/utils/avatarUtils';

const PROPERTY_SECTION_ID = '402A';

const PropertyInformation = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();

  // Get data from Redux store using memoized selectors
  const storeQuestions = useAppSelector(selectQuestionsBySubcategoryId(PROPERTY_SECTION_ID));
  const loading = useAppSelector(selectLoading);
  const userInputs = useAppSelector(selectUserInputsBySubcategoryId(PROPERTY_SECTION_ID));



  // Find the userInput for this subcategory
  const userInput = useMemo(
    () => userInputs.find((input: UserInput) => input.originalSubCategoryId === PROPERTY_SECTION_ID),
    [userInputs]
  );

  // Get the questionId from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const targetQuestionId = queryParams.get('questionId');



  // Fetch user inputs when component mounts using owner ID
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in PropertyInformation component');
          }
        } catch (error) {
          console.error('Error fetching owner ID in PropertyInformation component:', error);
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  // Scroll to the target question if specified in URL
  useEffect(() => {
    if (!loading && targetQuestionId) {
      setTimeout(() => {
        const element = document.getElementById(`question-${targetQuestionId}`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          element.classList.add('bg-yellow-100');
          setTimeout(() => {
            element.classList.remove('bg-yellow-100');
          }, 2000);
        }
      }, 500);
    }
  }, [loading, targetQuestionId]);



  // Prepare form values
  const savedAnswers = userInput ? convertUserInputToFormValues(userInput) : {};
  const existingInputId = userInput?._id || null;
  const validationSchema = buildValidationSchema(storeQuestions, Yup);
  const baseInitialValues = generateInitialValues(storeQuestions);
  const initialValues = { ...baseInitialValues, ...savedAnswers };


  const handleSubmit = async (values: Record<string, string>, { setSubmitting }: FormikHelpers<Record<string, string>>) => {
    try {
      if (!user || !user.id) {
        throw new Error('You must be logged in to save answers');
      }

      // Clean dependent answers before saving
      const cleanedValues = cleanDependentAnswers(values, storeQuestions);

      // Helper function to map question types to backend-accepted enum values
      const mapQuestionType = (type: string): string => {
        switch (type) {
          case 'dropdown':
            return 'choice';
          case 'currency':
          case 'password':
            return 'text';
          default:
            return type;
        }
      };

      const answersBySection = storeQuestions
        .reduce((sections: Record<string, Array<{
          index: number;
          originalQuestionId: string;
          question: string;
          type: string;
          answer: string;
        }>>, question) => {
          if (!sections[question.sectionId]) {
            sections[question.sectionId] = [];
          }
          const answer = cleanedValues[question.id];
          // Only include answers that have actual values (not empty, null, or undefined)
          if (answer !== undefined && answer !== null && answer !== '' && String(answer).trim() !== '') {
            sections[question.sectionId].push({
              index: sections[question.sectionId].length,
              originalQuestionId: question.id,
              question: question.text,
              type: mapQuestionType(question.type),
              answer: String(answer).trim()
            });
          }
          return sections;
        }, {});
      const formattedAnswersBySection = Object.entries(answersBySection).map(([sectionId, answers]) => ({
        originalSectionId: sectionId,
        isCompleted: true,
        answers: answers as Array<{
          index: number;
          originalQuestionId: string;
          question: string;
          type: string;
          answer: string;
        }>
      }));
      const hasAnswers = formattedAnswersBySection.some(section => section.answers.length > 0);
      if (!hasAnswers) {
        setSubmitting(false);
        return;
      }
      if (existingInputId) {
        await dispatch(updateUserInput({
          id: existingInputId,
          userData: {
            userId: user.id,
            categoryId: generateObjectId(),
            originalCategoryId: '10',
            subCategoryId: generateObjectId(),
            originalSubCategoryId: '402A',
            answersBySection: formattedAnswersBySection
          } as UserInput
        })).unwrap();
      } else {
        const userData: Omit<UserInput, '_id'> = {
          userId: user.id,
          categoryId: generateObjectId(),
          originalCategoryId: '10',
          subCategoryId: generateObjectId(),
          originalSubCategoryId: '402A',
          answersBySection: formattedAnswersBySection
        };
        await dispatch(saveUserInput(userData)).unwrap();
      }
      setSubmitting(false);
      navigate('/category/ownershipinfo/vehicleinformation');
    } catch (error: any) {
      setSubmitting(false);
    }
  };

  if (storeQuestions.length === 0 || loading) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }

  return (
    <div className="flex flex-col pt-20 min-h-screen">
      <AppHeader />
      <SubCategoryHeader
        title="Ownership Information"
        backTo="/dashboard"
        user={{
          name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
          email: user?.email || '<EMAIL>',
          avatar: createUserInfo(user).avatar
        }}
      />
      <SubCategoryTabs tabs={categoryTabsConfig.ownershipinfo} />
      <div className="container mx-auto px-6">
        <SubCategoryTitle
          mainCategory="Ownership Info"
          category="Property Information"
          description="These files contain questions to help you record your details so they're easy to find later."
        />
      </div>
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow-sm">

              <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
                enableReinitialize
              >
                {({ values, isSubmitting, isValid, dirty, setValues }) => {
                  const prevValuesRef = useRef<Record<string, any>>({});

                  // Handle dependent answers when specific values change
                  useEffect(() => {
                    const prevValues = prevValuesRef.current;
                    let shouldCleanDependents = false;

                    // Check if any dependency-triggering questions have changed
                    storeQuestions.forEach(question => {
                      if (question.dependsOn) {
                        const { questionId } = question.dependsOn;
                        if (prevValues[questionId] !== values[questionId]) {
                          shouldCleanDependents = true;
                        }
                      }
                    });

                    if (shouldCleanDependents && Object.keys(prevValues).length > 0) {
                      handleDependentAnswers(values, storeQuestions, setValues);
                    }

                    prevValuesRef.current = { ...values };
                  }, [values, setValues, storeQuestions]);

                  return (
                  <Form>
                    {[...storeQuestions]
                      .sort((a, b) => a.order - b.order)
                      .map((question) => (
                        <div
                          key={question.id}
                          id={`question-${question.id}`}
                        >
                          <QuestionItem
                            question={question}
                            formValues={values}
                          />
                        </div>
                      ))}
                    <div className="mt-8 flex justify-end">
                      <Button
                        type="submit"
                        disabled={isSubmitting || !isValid || !dirty}
                        className="bg-[#2BCFD5] hover:bg-[#19bbb5]"
                      >
                        Save & Continue
                      </Button>
                    </div>
                    <GoodToKnowBox
                      title="Editing my Answers"
                      description="Each topic below is a part of your ownership info, with questions to help you provide important information for you and your loved ones. Click any topic to answer the questions at your own pace—we'll save everything for you."
                    />
                    <SubCategoryFooterNav
                      leftLabel="All topics"
                      leftTo="/category/ownershipinfo"
                      rightLabel="Vehicle Information"
                      rightTo="/category/ownershipinfo/vehicleinformation"
                    />
                  </Form>
                  );
                }}
              </Formik>
            </div>
          </div>
          <div>
            <SearchPanel />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default PropertyInformation;
